import { z } from "zod";
import { NameSchema } from "./booking/schema";
import { VehicleDetailSchema } from "./rental/availability-contract";
import { Branch } from "./branch-contract";
import { ErrorSchema } from "./common";

export const UnauthorizedError = ErrorSchema.extend({
  code: z.literal("UNAUTHORIZED"),
  desc: z.literal("User is not authorized to access this resource"),
});

type CommonResponses = {
  401: typeof UnauthorizedError;
};

/**
 *
 *
 *  Tajeer <PERSON>hema
 *
 */

export const TajeerAgreementStatus = z.enum(["SUCCESS", "PENDING", "FAILED", "IN_PROGRESS"]);
export const TajeerAgreementType = z.enum(["TAJEER", "SECURITY_DEPOSIT", "TAMM"]);

const TajeerMetadata = z.object({
  type: TajeerAgreementType,
  validationStatus: TajeerAgreementStatus,
  validationDateTime: z.number(),
  saveStatus: TajeerAgreementStatus,
  saveDateTime: z.number(),
  contractNumber: z.string(),
  otpStatus: z.string(),
  otpDateTime: z.number(),
  sendCount: z.number(),
  createStatus: z.string(),
  createdAt: z.number(),
  depositAmount: z.string().optional(),
  cardLast4Digit: z.string().optional(),
  posMachine: z.string().optional(),
  failureReason: z
    .object({
      desc: z.string(),
      timestamp: z.string().optional(),
    })
    .nullable(),
  depositWithdrawnDetails: z.array(
    z.object({
      approvalCode: z.string(),
      cardLast4Digit: z.string(),
      posMachine: z.string(),
      withdrawnAmount: z.number(),
      withdrawnDate: z.number(),
      collectedBy: z.string(),
    })
  ),
  confirmCancelDriverAuthStatus: z.string().optional(),
  closeStatus: z.string().optional(),
  verifyDriverAuthIssueStatus: z.string().optional(),
  confirmDriverAuthIssueDateTime: z.number().optional(),
});

export const TajeerAgreement = z.object({
  id: z.number(),
  bookingNo: z.string(),
  agreementNo: z.string().nullable(),
  agreementVehicleId: z.string().nullable(),
  externalAuthorizationNumber: z.string(),
  type: TajeerAgreementType,
  status: TajeerAgreementStatus,
  skip: z.boolean(),
  skipReason: z.string().nullable(),
  skipBy: z.string().nullable(),
  metadata: TajeerMetadata,
  createdOn: z.number(),
  updatedOn: z.number(),
  createdBy: z.string(),
  updatedBy: z.string(),
});

export type TajeerAgreement = z.infer<typeof TajeerAgreement>;
export type WithCommonResponses<T> = T & CommonResponses;
export type ErrorSchema = z.infer<typeof ErrorSchema>;
export type DepositWithdrawnDetail = z.infer<typeof TajeerMetadata>["depositWithdrawnDetails"][number];

/**
 *
 *
 *  Promotion Schema
 *
 */

const PromotionSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: NameSchema,
  description: NameSchema.optional(),
  allDiscount: z.number().optional(),
  percentageDiscount: z.number().optional(),
  onlineDiscount: z.number().optional(),
  autoApplied: z.boolean().optional(),
  validFrom: z.number().optional(),
  validTo: z.number(),
  validCount: z.number().optional(),
  createdOn: z.number().optional(),
  updatedOn: z.number().optional(),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
  corporate: z.boolean().optional(),
  valid: z.boolean().optional(),
  enabled: z.boolean().optional(),
  promoCode: z.string().optional(),
});

export const PromotionResponseSchema = z.object({
  data: z.array(PromotionSchema).optional(),
});

export type Promotion = z.infer<typeof PromotionSchema>;
export type PromotionResponse = z.infer<typeof PromotionResponseSchema>;

/**
 * Agreement Schema
 */

const AgreementVehicleSchema = z.object({
  id: z.number(),
  plateNo: z.string(),
  vehicleMakeId: z.number(),
  vehicleModelId: z.number(),
  vehicleGroupId: z.number(),
  checkoutInspectionRefId: z.number(),
  status: z.string(),
});

const AgreementDriverAddressSchema = z.object({
  street: z.string().nullable(),
});

const AgreementDriverSchema = z.object({
  id: z.number(),
  driverCode: z.string(),
  driverUId: z.string(),
  name: z.string(),
  mobileNumber: z.string(),
  countryCode: z.number(),
  email: z.string(),
  dob: z.string(),
  hijrahDob: z.string(),
  title: z.string(),
  address: AgreementDriverAddressSchema,
});

const VehicleGroupSchema = z.object({
  id: z.number(),
  code: z.string(),
  displayName: z.string(),
  description: NameSchema,
  faceModelId: z.number(),
  enabled: z.boolean(),
  models: z.array(z.any()),
});

const AssignedVehicleSchema = z.object({
  plateNo: z.string(),
  plateNoAr: z.string(),
  vehicleGroupId: z.number(),
  vehicleGroup: VehicleGroupSchema,
  vehiclePlateInfo: VehicleDetailSchema,
  vehicleModelId: z.number(),
  model: z.object({
    id: z.number(),
    name: NameSchema,
    make: z.object({
      id: z.number(),
      name: NameSchema,
    }),
  }),
});

const PriceBreakdownSchema = z.object({
  rentalAmount: z.string(),
  insuranceAmount: z.string(),
  totalAddOnAmount: z.string(),
  dropOffAmount: z.string(),
  vatPercentage: z.string(),
  vatAmount: z.string(),
  totalSum: z.string(),
  trafficFineSum: z.string(),
  extraKmsChargeSum: z.string().optional(),
  extraFuelChargeSum: z.string().optional(),
});

const DiscountDetailSchema = z.object({
  discountPercentage: z.string(),
  totalDiscount: z.string(),
  promoCode: z.string(),
});

const TariffDetailSchema = z.object({
  dailyKmsAllowance: z.number(),
  extraKmsCharge: z.string(),
});

const AddOnSchema = z.object({
  id: z.number(),
  name: NameSchema,
});

const PriceDetailSchema = z.object({
  priceBreakdown: PriceBreakdownSchema,
  discountDetail: DiscountDetailSchema,
  tariffDetail: TariffDetailSchema,
  addOnIds: z.array(z.number()),
  addOns: z.array(AddOnSchema),
  includedComprehensiveInsurance: z.boolean(),
  insuranceIds: z.array(z.any()),
  insurances: z.array(
    z.object({
      id: z.number(),
      name: NameSchema,
    })
  ),
  soldDays: z.string(),
  soldDaysInSeconds: z.number(),
});

export const AgreementInvoiceSchema = z.object({
  agreementNo: z.string(),
  bookingId: z.number(),
  bookingNo: z.string(),
  referenceNo: z.string(),
  agreementVehicleId: z.number(),
  bookingDateTime: z.null(),
  pickupBranchId: z.number(),
  pickupBranch: Branch,
  pickupDateTime: z.number(),
  actualPickupDateTime: z.null(),
  dropOffBranchId: z.number(),
  dropOffBranch: Branch,
  dropOffDateTime: z.number(),
  actualDropOffDateTime: z.null(),
  driver: AgreementDriverSchema,
  status: z.string(),
  aggregatorName: z.string().optional(),
  debitorCode: z.null(),
  debtorName: z.string().optional(),
  debtorPO: z.string().optional(),
  promotionCode: z.null(),
  source: z.string(),
  assignedVehicle: AssignedVehicleSchema,
  priceDetail: PriceDetailSchema,
  remainingAmount: z.string(),
  driverPaidAmount: z.string(),
  refundApprovedAmount: z.string().optional(),
  refundRequestedAmount: z.string().optional(),
});

export type AgreementInvoice = z.infer<typeof AgreementInvoiceSchema>;
export type AgreementPriceDetail = z.infer<typeof PriceDetailSchema>;
