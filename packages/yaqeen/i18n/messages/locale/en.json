{"home": {"title": "Welcome to <PERSON><PERSON><PERSON>", "description": "This is a sample description"}, "LocaleSwitcher": {"ar": "Arabic", "en": "English", "label": "Language"}, "nav": {"home": "Home", "clients": "Clients", "management": "Fleet Management", "fleet": "Fleet", "vehicles": "Vehicles"}, "booking": {"rentalDuration": {"rental": "Rental amount", "duration": "for %1$s {days, plural, one %1$s other %1$s} {hours, plural, =0 {} other {and %1$s {hours, plural, one %1$s other %1$s}}}"}}, "payment": {"dialog": {"title": "Collect Payment", "collectDeposit": "Collect deposit"}, "amount": {"collect": "Amount to collect", "placeholder": "Enter amount", "remaining": "Remaining Balance = %1$s %1$s", "deposit": "Deposit amount", "error": "<PERSON><PERSON><PERSON>"}, "payment": {"method": "Payment Method", "cash": "Cash", "card": "Credit / Debit Card"}, "transaction": {"details": "Transaction details", "manualEntry": "Manual Entry", "approvalCode": "Approval Code", "enterApprovalCode": "Enter Approval Code", "posMachine": "POS Machine", "sendToPos": "Send to POS", "sendToPosDescription": "Send the transaction to the POS terminal", "send": "Send", "cancel": "Cancel", "select": "Select"}, "card": {"last4digits": "Last 4 digits of payment card", "placeholder": "Enter last 4 digits"}, "button": {"collect": "Collect Payment", "cancel": "Cancel", "addPayment": "Add Payment", "loading": "Processing payment"}, "toast": {"payment": {"success": "Payment collected successfully", "error": "Error collecting payment"}, "amount": {"error": "Invalid amount", "exceeds": "Payment amount limited to maximum remaining balance: {amount}"}}, "cash": {"collected": "Cash collected"}, "search": {"placeholder": "Search payments", "noResults": "No payments found"}, "download": {"downloadFailed": "Download Failed", "failedToDownloadReceipt": "Failed to download receipt", "printFailed": "Print Failed", "failedToOpenPrintWindow": "Failed to open print window", "bookingDetails": "Booking details", "downloadAgreement": "Download Agreement", "unexpectedError": "An unexpected error occurred", "receiptNotFound": "Receipt found"}, "sidesheet": {"payment_title": "Payment Details", "payment_id": "Payment ID", "refund_title": "Refund Details", "record_by": "Recorded by", "account_holder_name": "Account holder name", "iban_number": "IBAN Number", "bank_name": "Bank Name", "iban_letter": "IBAN letter", "preview": "Preview"}, "error": {"amount": {"exceeds": "Payment amount cannot exceed the remaining balance", "invalid": "Invalid amount"}}, "remaining": {"balance": "Remaining balance = {amount}"}, "receiptNo": "Receipt No.", "transactionTime": "Transaction Time", "type": "Type", "status": "Status", "actions": "Actions", "viewDetails": "View Details", "amountDetails": {"collect": "Amount to collect", "placeholder": "Enter amount", "remaining": "Remaining Balance = %1$s %1$s", "deposit": "Deposit amount", "error": "<PERSON><PERSON><PERSON>"}, "transactionAmount": "Amount", "columns": {"Payment details": "Payment details", "View payment details": "View payment details", "Pos": "POS", "Online": "Online", "Open menu": "Open menu", "Success": "Success", "Cash": "Cash", "Requested": "Requested", "Bank transfer": "Bank transfer", "pos": "POS", "online": "Online", "success": "Success", "cash": "Cash", "requested": "Requested", "bank_transfer": "Bank transfer", "cancelled": "Cancelled"}}, "authorization": {"title": "Authorization", "tajeer": {"title": "<PERSON><PERSON><PERSON> authorization", "description": "Mandatory for all Saudi citizens, residents and tourists", "authorized": "Authorized", "failed": "Authorization Failed", "contractNo": "Tajeer contract no.", "viewContract": "View contract", "authorizeCustomer": "Authorize customer", "tryManually": "Try Manually", "tryAgain": "Try Again", "enterCode": "Enter code", "codeSent": "We've sent a code to customer's mobile number", "validateCode": "Validate code", "enterContractNumber": "Enter <PERSON> contract number", "openTajeerWebsite": "Open Tajeer website", "contractNumberPlaceholder": "<PERSON><PERSON><PERSON> Contract Number", "submit": "Submit", "failedTitle": "Failed to authorize <PERSON><PERSON><PERSON>", "Tajeer skipped": "<PERSON><PERSON><PERSON> skipped"}, "tamm": {"title": "Tamm authorization", "failedTitle": "Failed to authorize <PERSON><PERSON>", "description": "Mandatory for bike rentals and Debtor customers", "authorizeCustomer": "Authorize customer", "authNumber": "Authorization number", "authOn": "Authorized On"}, "securityDeposit": {"title": "Security deposit", "description": "Suspicious/first time customers, luxury vehicle", "collectDeposit": "Collect deposit", "collected": "Collected", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "Deposit Details": "De<PERSON>sit Details", "SAR": "SAR", "Success": "Success", "Credit card deposit": "Credit card deposit", "View Details": "View Details", "Amount": "Amount", "Authorized On": "Authorized On", "Receipt number": "Receipt number", "Last 4 digits": "Last 4 digits", "POS": "POS", "Collected By": "Collected By", "POS machine no": "POS machine no", "withdrawnFromDeposit": "Withdrawn from deposit", "withdrawnAmount": "Withdrawn amount", "withdrawnOn": "Withdrawn on", "POS machine no.": "POS machine no."}, "otp": {"enterCode": "Enter code", "codeSent": "We've sent a code to customer's mobile number", "expired": "OTP Expired", "expiredDescription": "The OTP has expired. Please try again.", "resend": "Resend OTP", "resendSuccess": "OTP Resent", "resendSuccessDescription": "The OTP has been resent to the customer's mobile number", "validateCode": "Validate code", "resentTitle": "OTP Resent", "resentDescription": "The OTP has been resent to the customer's mobile number", "resendFailedTitle": "Failed to resend OTP", "resendFailedDescription": "An error occurred while resending OTP"}, "success": {"title": "Agreement Started Successfully", "description": "A copy of the rental agreement has been sent to the customer's email", "viewDetails": "View agreement details", "backToBookings": "Go to Ongoing Bookings"}, "errors": {"failedToAuthorize": "Failed to authorize <PERSON><PERSON><PERSON>", "failedToValidate": "Failed to validate <PERSON><PERSON><PERSON>", "failedToResendOTP": "Failed to resend OTP", "resendOTPError": "An error occurred while resending OTP", "failedToAuthorizeTamm": "Failed to authorize <PERSON><PERSON>"}, "download": {"downloadFailed": "Download Failed", "failedToDownloadAgreement": "Failed to download agreement", "printFailed": "Print Failed", "failedToOpenPrintWindow": "Failed to open print window", "bookingDetails": "Booking details", "downloadAgreement": "Download Agreement", "unexpectedError": "An unexpected error occurred", "agreementNotFound": "Agreement not found"}}, "DataTable": {"RowActions": {"openMenu": "Open menu", "startAgreement": "Start agreement", "editBooking": "Edit booking", "cancelBooking": "Cancel booking", "assignVehicle": "Assign vehicle", "moreDetails": "More details"}}, "bookings": {"columns": {"bookingNo": "Booking no.", "bookingDate": "Booking date", "pickupTime": "Pickup time", "dropOffTime": "Drop-off time", "driver": "Driver", "vehicle": "Vehicle", "source": "Source", "status": "Status", "total": "Total (SAR)", "na": "N/A", "upcoming": "Upcoming", "agreementNo": "Agreement #", "group": "Group", "flight": "Flight", "dues": "Dues (SAR)", "notes": "Notes", "pendingAction": "Pending Action", "actions": {"endAgreement": "End agreement", "startAgreement": "Start agreement", "viewBooking": "View Booking"}, "Completed": "Completed", "Ongoing": "Ongoing", "Cancelled": "Cancelled", "Upcoming": "Upcoming", "Paid": "Paid", "Unpaid": "Unpaid", "Late Return": "Late Return", "late return": "Late Return", "TAMM Closure Pending": "TAMM Closure Pending", "TAJEER Extension Pending": "TAJEER Extension Pending", "TAJEER Pending": "TAJEER Pending", "TAJEER Closure Pending": "TAJEER Closure Pending", "TAMM Pending": "TAMM Pending", "LATE_RETURN": "Late Return", "COMPLETED": "Completed", "ONGOING": "جاري", "No dues": "No dues", "noShow": "No Show", "ongoing": "Ongoing", "lateReturn": "Late Return", "suspended": "Suspended", "cancelled": "Cancelled", "completed": "Completed", "No show": "No Show", "Late return": "Late Return"}, "actions": {"endAgreement": "End agreement", "startAgreement": "Start agreement", "viewBooking": "View Booking"}, "upcoming": "Upcoming", "bookingPrefix": "Booking ##{bookingNumber}", "details": {"bookingDetails": "Booking details", "pickupBranch": "Pickup branch", "dropOffBranch": "Drop-off branch", "pickupDate": "Pickup date", "dropOffDate": "Drop-off date", "customerDetails": "Customer details", "vehicleDetails": "Vehicle details", "model": "Model", "plateNo": "Plate No.", "bookingSummary": "Booking Summary"}, "create": {"pageTitle": "Create New Booking", "redirecting": "Redirecting to assign a vehicle page..."}, "customerTypes": {"individual": "Individual", "corporate": "Corporate", "government": "Government", "tourist": "Tourist"}, "bookingTypes": {"walkIn": "WALKIN", "online": "Online", "corporate": "Corporate", "government": "Government"}, "myBookings": "My bookings", "searchPlaceholder": "Search", "ongoing": "Ongoing", "completed": "Completed", "needsAction": "Needs action", "allBookings": "All bookings", "createBooking": "Create a booking", "filters": {"dropOffTime": "Drop-off time", "next2Hours": "Next 2 hours", "next6Hours": "Next 6 hours", "today": "Today", "next48Hours": "Next 48 hours", "thisWeek": "This week", "thisMonth": "This month", "paid": "Paid", "pending": "Pending", "payment": "Payment", "pickupTime": "Pickup time", "status": "Status", "upcoming": "Upcoming", "noShow": "No Show", "ongoing": "Ongoing", "lateReturn": "Late Return", "suspended": "Suspended", "cancelled": "Cancelled", "completed": "Completed", "source": "Source", "walkIn": "Walk in", "corporate": "Corporate", "online": "Online", "carla": "<PERSON>", "yolcu": "<PERSON><PERSON><PERSON>", "lumi": "<PERSON><PERSON>", "late return": "Late Return", "no show": "No Show"}, "searchFilters": {"bookingNo": "Booking No", "mobileNumber": "Mobile No", "driverName": "Driver Name", "agreementNo": "Agreement No"}, "emptyMessage": "No bookings found", "home": "Home", "agreement": "Agreement", "on": "on"}, "tariff": {"rentalTariff": "Rental Tariff", "rentalTariffSubTitle": "All available rental tariff and base breakdown of current year", "rateCard": "Rate Card", "addNewRate": "Add New Rate", "uploadSheetTemplate": "Upload sheet template", "useExistingRateCard": "Use existing rate card", "createNew": "Create new", "loadMore": "Load More", "noCardsAvailable": "No Cards Available", "cancel": "Cancel", "save": "Save", "continue": "Continue", "delete": "Delete", "modify": "Modify", "lastUpdated": "Last updated", "validPeriod": "Valid period", "createdBy": "Created by", "approvalDate": "Approval date", "download": "Download", "downloadAndModify": "Download and Modify", "stopSales": "Stop Sales", "name": "Name", "startDate": "Start Date", "endDate": "End Date", "rates": {"existingRateCard": "Existing rate card", "existingRateCardDescription": "Select any pre-existing rate card as a base for faster setup"}, "addons": {"addAddOnsRatesBtn": "Add add-ons rates", "addAddOnsRatesTitle": "Add new add-on Rate", "addOnsRates": "Add Ons Rates", "noAddOns": "There are no Add-ons.", "addonsIds": "Addons Ids", "applicableGroup": "Applicable group", "type": "Type", "price": "Price", "priceUnit": "Price Unit", "description": "Description"}, "debtor": {"addDebtor": "Add New Debtor", "debtorRate": "Debtor Rate", "debtorRateSubTitle": "Track and manage debtor profiles efficiently and stay updated with their tariff rates.", "newDebtorCard": "New Debtor Card", "newDebtorCardSubTitle": "You can create the multiple debtor & set their discount rate.", "startDate": "Start Date", "endDate": "End Date", "debtorCard": "Debtor Cards", "fixedRate": "Fix rate", "fixedRateDescription": "Offers a consistent, unchanging rate for specific services or car groups, ensuring predictable pricing.", "dynamicRate": "Discount based", "dynamicRateDescription": "Provides discounts on selected car groups, ensuring cost savings for specific rentals.", "baseCardDetails": "Base Card details", "debtorDetails": "Debtor details"}}, "closeAgreement": {"Assign new vehicle": "Assign new vehicle", "Inspection Report": "Inspection Report", "Pickup Inspection": "Pickup Inspection", "Inspector's remarks": "Inspector's remarks", "New Damage": "New Damage", "Dashboard readings": "Dashboard readings", "Item": "<PERSON><PERSON>", "Pickup": "Pickup", "Drop-off": "Drop-off", "Difference": "Difference", "Extra Charge (SAR)": "Extra Charge (SAR)", "Damages/Penalties": "Damages/Penalties", "Basic insurance selected": "Basic insurance selected", "There are no damages/penalties added": "There are no damages/penalties added", "Add new damage/penalty": "Add new damage/penalty", "success": {"title": "Agreement Closed Successfully", "description": "A copy of the rental agreement has been sent to the customer's email", "viewDetails": "View agreement details", "backToBookings": "Go to Ongoing Bookings"}, "Price breakdown": "Price breakdown", "SAR": "SAR", "Rental": "Rental", "Insurance": "Insurance", "Discount": "Discount", "Add-ons": "Add-ons", "Drop-off fee": "Drop-off fee", "VAT": "VAT", "Total": "Total", "Paid amount": "Paid amount", "Remaining balance": "Remaining balance", "Booking details": "Booking details", "Booked on": "Booked on", "No discount": "No discount", "Amount": "Amount", "Customer preferences": "Customer preferences", "You can edit them in next steps": "You can edit them in next steps", "Group": "Group", "NA": "N/A", "Severity": "Severity", "Amount: SAR": "Amount: SAR", "View Report": "View Report", "View": "View", "Description": "Description", "Type": "Type", "Report": "Report", "Payable (SAR)": "Payable (SAR)", "Edit KMs reading": "Edit KMs reading", "Check-out KMs reading": "Check-out KMs reading", "Check-in KMs reading": "Check-in KMs reading", "Check-out fuel level": "Check-out fuel level", "Check-in fuel level": "Check-in fuel level", "Extra charge (Including VAT)": "Extra charge (Including. VAT)", "Waive extra charge": "Waive extra charge", "Waive reason": "Waive reason", "Loyal customer": "Loyal customer", "Complementary waive": "Gesture of apology", "Other reason": "Other reason", "I got approval from the branch supervisor/manager": "I got approval from the branch supervisor/manager", "Cancel": "Cancel", "Comprehensive insurance selected": "Comprehensive insurance selected", "Pay1500": "Pay up to SAR 1,500 with a police report.", "Apply charge": "Apply charge", "Uploading file": "Uploading file", "Attach police report": "Attach police report", "Low": "Low", "Medium": "Medium", "Major": "Major", "Penalties & Damages costs": "Penalties & Damages costs", "Payable amount": "Payable amount (excl. VAT)", "File selected": "File selected", "KMs": "KMs", "KM": "KM", "Pay up to": "Pay up to", "with a police report": "with a police report.", "cta": {"back": "Back", "save": "Exit", "continue": "Save & Continue", "close": "Close Agreement", "cancel": "Cancel"}, "dialog_title": "Close Agreement?", "dialog_desc": "Closing this agreement will also close associated TAMM or Tajeer authorizations. This action cannot be undone.", "Drop-off Inspection": "Drop-off Inspection", "trafficFines": {"title": "Traffic Fines", "loading": "Loading traffic fines...", "total": "Total", "trafficFine": "Traffic Fine", "unknownViolation": "Unknown Violation", "paid": "Paid", "invoice": {"notFound": "Invoice not found", "checkInvoiceNo": "Check the invoice number and try again", "downloadFailed": "Download Failed", "failedToDownloadInvoice": "Failed to download invoice", "printFailed": "Print Failed", "failedToOpenPrintWindow": "Failed to open print window"}}, "No inspection details": "No inspection details", "myBookings": "My bookings", "createBooking": "Create a booking", "endAgreement": "End agreement", "extendBooking": "Extend booking", "closeAgreement": "Close rental agreement", "agreementNo": "Agreement No", "Edit damage/penalty": "Edit damage/penalty", "Add damage/penalty description": "Add damage/penalty description", "Select severity": "Select severity", "Enter payable amount here": "Enter payable amount here", "Choose police report file": "Choose police report file", "Save changes": "Save changes", "Add damage or penalty": "Add damage/penalty", "Updating...": "Updating...", "Adding...": "Adding...", "Error": "Error", "Saving...": "Saving...", "Please provide a damage description": "Please provide a damage description", "Failed to upload police report file. Please try again.": "Failed to upload police report file. Please try again.", "Success": "Success", "Damage/penalty has been updated successfully": "Damage/penalty has been updated successfully", "Damage/penalty has been added successfully": "Damage/penalty has been added successfully", "Failed to update damage/penalty. Please try again.": "Failed to update damage/penalty. Please try again.", "Failed to add damage/penalty. Please try again.": "Failed to add damage/penalty. Please try again.", "Fuel Level": "Fuel Level", "Inspection details": "Inspection details", "Payment": "Payment", "Authorization": "Authorization", "Saving": "Saving...", "DAMAGE": "DAMAGE", "Customer selection": "Customer selection", "Edit fuel level": "Edit fuel level", "Updating": "Updating...", "Adding": "Adding...", "Failed to upload police report file Please try again": "Failed to upload police report file. Please try again", "Failed to update damage penalty Please try again": "Failed to update damage/penalty. Please try again", "Failed to add damage penalty Please try again": "Failed to add damage/penalty. Please try again", "trafficFine": "Traffic Fine"}, "promotions": {"promotions": "Promotions", "createCustomizedCampaigns": "Create customized campaigns for every type of customer", "createNewPromotion": "Create New Promotion", "promotionDetails": "Promotion Details", "discount": "Discount(%)", "startDate": "Start Date", "endDate": "End Date", "branches": "Branches", "carGroups": "Car Groups", "redemptionLimit": "Redemption Limit", "durationLimit": "Duration Limit", "eligibleDays": "Eligible Days", "paymentOptions": "Payment Options", "payOnline": "Pay online", "payAtBranch": "Pay at branch", "terms": "Terms", "cancel": "Cancel", "save": "Save", "day": "Days", "voucherType": "Voucher Type", "promotionalCampaign": "Promotional Campaign", "corporateCDP": "Corporate CDP", "oneTimePromo": "One-time Promo", "promotionalCampaignDescription": "Discount that given for a specific occasion such as Bank discount, company events etc", "corporateCdpDescription": "Promo given to corporate partners’ employees ", "oneTimePromoDescription": "Vouchers that are redeemable only on for special offers ", "promotionNameEn": "Promotion Name", "promotionNameAr": "اسم العرض", "details": "Details", "code": "Code", "payOnlineDescription": "Ideal for customers who prefer to confirm their bookings instantly with upfront payments.", "payAtBranchDescription": "Perfect for those who value in-person transactions and added convenient at the branch.", "promotionalBanner": "Promotional Banner", "promotionalBannerDescription": "The image will be used to appear on the offers page of the app and website", "selectBranches": "Select Branches", "selectCarGroups": "Select Car Groups", "chooseBranches": "Choose Branches", "close": "Close", "showInApp": "Show promotion in the app", "showInAppDescription": "Select whether or not the promotion is visible in Lumi mobile experience.", "eligibleDaysDescription": "Specify the exact days of the week this promo will be available for", "validDate": "Valid Date", "termsAndConditions": "Terms & Conditions", "termsAndConditionsPlaceholder": "Write the detailed terms and conditions, including user responsibilities, privacy policies, and legal disclaimers.", "promotionName": "Promotion Name", "redemptionLimitDescription": "Specify number of redemptions", "numberOfRedemptions": "Number of Redemptions", "durationLimitDescription": "Specify minimum or maximum length of the rental", "select": "Select", "selectedBranches": "{count, plural, =0 {No branches selected} =1 {one branch selected} other {# branches selected}}.", "eligibleEmailDomains": "Eligible Email Domains", "noOfVouchers": "Number of Vouchers", "vouchersDescription": "A list of codes will be generated after the promotion is created", "descriptionEn": "Description", "descriptionAr": "وصف", "editPromotion": "Edit Promotion", "editPromotionDescription": "Edit the promotion details", "selectAll": "Select All", "activate": "Activate", "deactivate": "Deactivate", "branchesCount": "{count, plural, =0 {Branches} =1 {Branch} other {Branches}}.", "edit": "Edit", "statusTitle": "Are you sure you want to {status} this promotion?", "preview": "Preview", "remainingCount": "Remaining Count", "eligibleEmailDomainsDescription": "press enter or click away to add new domain"}, "vehicle-status": {"title": "Vehicle status", "description": "Choose the vehicle status after replacing the vehicle", "needs-service": "Needs refueling or cleaning", "workshop": "Send to workshop", "ready": "Mark vehicle as ready"}, "replace-reason": {"title": "Replace reason", "description": "Select the reason for the vehicle replacement", "vehicle-issue": "Vehicle issue or breakdown", "vehicle-issue-description": "The vehicle has a critical mechanical or system failure.", "accident-damage": "Accident or vehicle damage", "accident-damage-description": "The vehicle was in an accident or has visible damage", "maintenance": "Maintenance", "maintenance-description": "The vehicle is due to maintenance", "customer-unsatisfied": "Customer unsatisfied", "customer-unsatisfied-description": "The customer asked for vehicle replacement"}, "pricing": {"discount": "Discount", "priceBreakdown": "Price breakdown", "rentalLabel": "Rental", "rentalPeriod": "for {days, plural, =0 {} one {# day} other {# days}} {hours, plural, =0 {} one {& # hour} other {& # hours}}", "insurance": "Insurance", "addOns": "Add-ons", "dropOffFee": "Drop-off fee", "vat": "VAT", "total": "Total", "paidAmount": "Paid amount", "remaining": "Remaining", "duration": "{days, plural, =0 {0 days} one {# day} other {# days}} {hours, plural, =0 {} one {: # hour} other {: # hours}}", "errors": {"fetchQuoteDetails": "Could not fetch quote details (Error: {errorCode})", "failedToLoadPricing": "Failed to load pricing information", "showingPartialInfo": "Showing partial information only"}, "insurancee": "Insurance", "Discount": "Discount", "Rental": "Rental", "SAR": "SAR", "Pickup": "Pickup", "Drop-off": "Drop-off", "days": "Days", "hours": "Hour", "Booking summary": "Booking summary", "Price breakdown": "Price breakdown", "extraFuelCharges": "Extra Fuel Charges", "extraKmCharges": "Extra KM Charges", "trafficFine": "Traffic Fine", "comprehensiveInsurance": "Comprehensive insurance", "free": "Free", "addonFallback": "Addon {number}", "basicInsurance": "Basic insurance", "damagesPenalties": "Damages/Penalties", "Driver Pays": "Driver Pays", "Company Pays": "Company Pays"}, "common": {"breadcrumbs": {"home": "Home", "myBookings": "Branch Bookings", "createBooking": "Create Agreement", "All Bookings": "All Bookings"}, "actions": {"back": "Back", "save": "Save", "exit": "Exit", "saveAndExit": "Exit", "continue": "Continue", "saveAndContinue": "Save and Continue", "assignAndContinue": "Assign and Continue", "createBooking": "Continue", "createAgreement": "Create Agreement", "loading": "Loading", "export": "Export to Excel", "goBack": "Go Back", "clear": "Clear", "createBookingDebtor": "Create Booking", "failure": "Failure", "Quote ID is required": "Quote ID is required"}, "errors": {"somethingWentWrong": "An unexpected error occurred, please try again.", "genericError": "An unexpected error occurred, please try again.", "agreementCreation": "Failed to create agreement, please try again.", "unexpectedError": "An unexpected error occurred, please try again.", "fetchQuoteDetails": "Failed to fetch quote details", "failedToFetchBranches": "Unable to load branches, please try again.", "failedToFetchBookings": "Unable to load bookings, please try again.", "unauthorized": "Unauthorized", "oops": "Oops! Our code took a coffee break, please try again.", "dontWorry": "Don't worry, even the best of us need a restart sometimes!"}, "toast": {"changesSaved": "Changes saved", "bookingUpdated": "Booking updated successfully", "bookingCreated": "Booking created", "bookingCreatedSuccess": "Booking created successfully"}, "bookingCreation": {"creatingBooking": "Creating booking", "waitingMessage": "Please wait a moment while we create your booking and redirect to the payment page"}, "notFound": {"noResults": "No results found"}, "loading": "Loading...", "tryAgain": "Try again", "usermenu": {"switchBranch": "Switch", "switchBranch2": "Switch Branch", "selectBranch": "Select a branch from the list", "logout": "Logout", "switchToEnglish": "Switch to English", "switchToArabic": "التبديل إلى اللغة العربية", "Arabic": "العربية", "English": "English", "changeLanguage": "Change Language"}, "filters": {"selected": "Selected", "noResultsFound": "No results found", "clearFilters": "Clear filters"}, "pagination": {"showing": "Showing {start}-{end} of {total}", "firstPage": "Go to first page", "previousPage": "Go to previous page", "nextPage": "Go to next page", "lastPage": "Go to last page", "of": "of"}, "home": "Home", "welcome": "Welcome", "View Bookings": "View Bookings", "Manage your rental bookings": "Manage your rental bookings", "Branch Settings": "Branch Settings", "Configure branch settings": "Configure branch settings", "Cash Register": "Cash Register", "View cash register details": "View cash register details", "Select branch": "Select branch", "Enter debtor PO": "Enter debtor PO"}, "navigation": {"bookingSteps": {"bookingdetails": "Booking details", "driverdetails": "Driver details", "assignavehicle": "Assign a vehicle", "insuranceandextras": "Insurance & extras", "payment": "Payment", "authorization": "Authorization", "additionaldetails": "Additional details"}, "dropOff": "Drop-off", "Booking Confirmed": "Booking Confirmed", "The booking has been successfully created": "The booking has been successfully created", "Total": "Total", "SAR": "SAR", "Booking": "Booking", "pickup": "Pickup", "days": "Days", "hours": "Hours", "Group": "Group", "Back to my bookings": "Back to my bookings"}, "create": {"pageTitle": "Create New Booking", "redirecting": "Redirecting to assign a vehicle page..."}, "driver": {"mainDriver": "Main Driver", "customerId": "Customer's ID number", "idNumber": "Driving license, Saudi national ID, iqama ID, GCC ID, or passport", "searchPlaceholder": "Search by document id...", "createNew": "Create a new driver", "searching": "Searching drivers...", "noDriversFound": "No drivers found."}, "vehicles": {"availableVehicles": "Available vehicles", "readyVehiclesCount": {"one": "1 ready vehicle", "other": "# ready vehicles"}, "allGroups": "All groups", "upgradeOptions": "Upgrade options", "selecting": "Selecting", "select": "Select", "noResults": "No results found", "modifyFilters": "Try modifying or resetting the filters", "viewAllBranchVehicles": "View all branch vehicles", "resetFilters": "Reset filters", "vehicleDetails": "Vehicle details", "viewVehicleDetails": "View vehicle details here.", "moreDetails": "More details", "km": "KM", "sar": "SAR", "group": "Group", "na": "N/A", "allIncluded": "All included", "preferenceTypes": {"exactMatch": "Exact Match", "similar": "Similar", "upgrade": "Upgrade", "downgrade": "Downgrade", "none": "None"}, "viewMore": {"prefix": "View more", "similar": "similar", "upgrades": "upgrades", "matches": "matches"}, "tooltips": {"kilometers": "KMs", "fuel": "Fuel", "inspected": "Inspected", "inclusionDetails": "Rental, insurance & extras"}, "errors": {"title": "Error", "defaultMessage": "An error occurred", "vehicleLockFailed": "Failed to lock vehicle", "pricingFailure": "Pricing error", "missingQuoteId": "Missing quote ID", "unexpectedError": "Unexpected error"}, "readyVehicles": "Ready vehicles", "inspection": {"neverInspected": "Never inspected", "minutesAgo": "{count} minutes ago", "hoursAgo": "{count} hours ago", "yesterday": "Yesterday", "today": "Today", "sameWeek": "Same week", "moreThanAWeek": "More than a week", "onDay": "on {day}", "onDate": "on {date}"}, "vehicleStatus": "Vehicle status", "readyToRent": "Ready to rent", "viewReport": "View report", "features": "Features", "upgradeVehicle": "Upgrade vehicle", "selectVehicle": "Select vehicle", "cleaning": {"neverCleaned": "Never cleaned", "minutesAgo": "{count} minutes ago", "hoursAgo": "{count} hours ago", "yesterday": "Yesterday", "onDay": "on {day}", "onDate": "on {date}"}, "selectedVehicle": "Selected vehicle", "changeVehicle": "Change vehicle", "noFeatures": "No features available", "loadingFeatures": "Loading features...", "emptyState": {"noReadyVehicles": "There are no ready vehicles available in the branch", "viewAll": "View all branch vehicles", "onlyDowngrade": "Only downgrade options are available", "noExactmatch": "There are no exact or similar vehicle available", "customerPreference": "Customer&apos;s preference"}, "specifications": {"doors": "Doors", "gears": "Gears", "fuelType": "Fuel type", "cylinders": "Cylinders", "driveType": "Drive type", "engineSize": "Engine size", "horsepower": "Horsepower", "convenience": "Convenience", "measurements": "Measurements", "transmission": "Transmission", "seat": "<PERSON><PERSON>", "exteriorFeatures": "Exterior features", "interiorFeatures": "Interior features", "bodyType": "Body type", "noFeatures": "No features available", "fuelCapacity": "Fuel capacity"}, "success": {"vehicleSelected": "Vehicle selected successfully"}}, "insurance": {"title": "Insurance", "thirdParty": {"description": "In case of accident, a maximum of SAR {deductible} will be charged if at fault.", "description_default": "In case of accident, a maximum will be charged if at fault."}, "pricing": {"withDeductible": "+ SAR {amount}", "free": "Free", "perDay": "{amount}/day"}}, "extras": {"kmsAllowance": {"title": "KMs allowance", "standardKm": "{km} KM", "unlimitedKm": "Unlimited KM"}, "addOns": {"title": "Extras", "noExtras": "No extras available"}, "pricing": {"free": "Free", "addOn": "+ {currency} {amount}"}, "errors": {"fetchQuoteDetails": "Failed to fetch quote details", "failedToFetch": "Failed to fetch extras"}}, "drivers": {"title": "Driver Details", "labels": {"mainDriver": "Main driver"}, "sections": {"driverDetails": "Driver Details", "drivingLicense": "Driving License"}, "fields": {"firstName": "First Name", "lastName": "Last Name", "mobile": "Mobile", "email": "Email", "nationality": "Nationality", "idNumber": "ID number", "number": "Number", "idType": "ID type", "dateOfBirth": "Date of birth", "dateOfBirthHijri": "Date of Birth - <PERSON><PERSON><PERSON>", "dateOfBirthGregorian": "Date of Birth - <PERSON><PERSON>", "idExpiryDate": "ID expiry date", "idExpiryDateHijri": "ID Expiry Date - <PERSON><PERSON><PERSON>", "idExpiryDateGregorian": "ID Expiry Date - <PERSON><PERSON>", "idIssuedCountry": "ID Issued Country", "address": "Address", "country": "Country", "licenseNumber": "License number", "licenseExpiryDate": "License Expiry Date", "licenseExpiryDateHijri": "License Expiry Date - Hijri", "licenseExpiryDateGregorian": "License Expiry Date - <PERSON><PERSON>", "borderNumber": "Border number", "iqamaNumber": "Iqama number", "iqamaExpiryDate": "Iqama expiry date", "passportIssueCountry": "Passport issue country", "passportNumber": "Passport number", "passportExpiryDate": "Passport expiry date", "expiryDate": "Expiry date"}, "idTypes": {"saudiNational": "ID", "gcc": "GCC", "resident": "Resident", "visitor": "Visitor", "passport": "Passport", "iqama": "<PERSON><PERSON><PERSON>", "saudiNational2": "Saudi National"}, "actions": {"createNewDriver": "Create a new driver", "createDriver": "Create Driver", "saveChanges": "Save Changes", "removeDriver": "Remove driver", "removeDriverDescription": "You can choose another driver after removal.", "cancel": "Cancel", "remove": "Remove", "getBorderNumber": "Get Border Number"}, "values": {"na": "N/A", "fillIt": "Missing"}, "search": {"idNumberTitle": "Customer's ID number", "idNumberDescription": "Driving license, Saudi national ID, iqama ID, GCC ID, or passport", "placeholder": "Search by document id...", "searching": "Searching drivers...", "noResults": "No drivers found.", "noDriverFound": "No driver found."}, "dialogs": {"cancelCreation": {"title": "Cancel Driver Creation", "description": "Are you sure you want to cancel? All entered information will be lost.", "confirm": "Yes, <PERSON>cel", "continue": "Continue Editing"}, "cancelEdit": {"title": "Cancel changes?", "description": "All changes you have made will be discarded", "confirm": "Cancel changes", "continue": "Keep editing"}}, "success": {"driverCreated": "Driver created successfully", "driverUpdated": "Driver details updated successfully"}, "errors": {"failed": "Failed to create driver", "serverFailure": "Server error", "failedToCreateDriver": "Failed to create driver", "failedToUpdateDriver": "Failed to update driver details", "missingQuoteId": "Quote ID is missing", "failedToAssignDriver": "Failed to assign driver", "unexpectedError": "An unexpected error occurred", "driverCreationFailed": "Driver creation failed"}}, "datePicker": {"selectMonth": "Select Month", "pickDate": "Pick a date"}, "VehicleListing": {"Assigned": "Assigned", "Vehicle": "Vehicle", "OutofService": "Out of Service", "Location": "Location", "ReadyforRental": "Ready for Rental", "MyVehicles": "My Vehicles", "OpenNRM": "Open NRM", "NRMs": "NRMs", "Unassigned": "Unassigned", "Rented": {"Reason": {"Maintenance": "Maintenance", "Fuel/Cleaning": "Fueling/Cleaning", "Drop-offLocation": "Drop-off Location", "Drop-offDate&Time": "Drop-off date & time", "WorkshopTransfer": "Workshop transfer"}, "TodayDrop-off": "Drop-off Today", "Outofservicemovement": "Move to out of service", "2hoursDropOff": "Drop-off in 2 hours", "WaitingPeriod": "Waiting Period", "PassedTime": "Passed Time"}, "NeedsPrep": {"Reason": "Reason", "MovingVehicleDescription": "Moving the vehicle to available will make it available to be assigned to drivers, are you sure you want to move the vehicle?", "MoveToAvailable": "Move to available", "MovingVehicle": "Move vehicle", "MovementTitle": "Move to available"}, "MovingVehicle": {"Description": "Choose reason to move vehicle to out of service.", "OpeningNRM": {"EmployeeID": "Employee ID (Optional)", "CheckoutTime": "Checkout time", "RemarkDescription": "Enter a remark if you have any", "OutOfServiceDescription": "The vehicle will automatically be moved to Out of Service for being at work shop.", "OutOfServiceTitle": "Vehicle will be moved to out of service", "OptionalRemarks": "Remarks (optional)", "MoveTo": "Move to", "CheckoutDetails": "Check out Details"}, "ReasonTitle": "Reason for out of service", "MovedSuccessfullyTitle": "Vehicle moved successfully", "Reason": {"RegistrationExpired": "Registration Expired", "Operationcardexpired": "Vehicle operation card expired", "InsuranceExpired": "Insurance Expired"}, "ReasonDescription": "choose reason for out of service", "MovedSuccessfulyDescription": "The vehicle has been moved to out of service successfully.", "dialog": {"title": "Moving Vehicle"}}}, "NeedsPrep": "Needs Prep", "Rented": "Rented", "OpeningNRM": "Open a new NRM", "VehicleListing_NRM_CheckInDetails": "Check in Details", "VehicleListing_NRM_Error_KMIn_Title": "KM in seems higher than usual", "VehicleListing_NRM_Success_Title": "NRM closed successfully", "VehicleListing_NRM_Startedin2hours": "Started in last 2 hours", "VehicleListing_NRM_KMIn": "KM In", "VehicleListing_NRM_Success_Description": "NRM has been closed and the vehicle has been moved to out of service.", "VehicleListing_NRM_FuelIn": "Fuel In", "VehicleListing_NRM_Error_KMIn_Description": "The entered KM for arriving vehicle seems higher than usual. Close NRM if its right or double check it.", "VehicleListing_NRM_KMInCheck": "Check KM In", "VehicleListing_NRM_CloseNRM": "Close NRM", "VehicleListing_OutofService_Reason_Accident": "Accident / at the workshop", "VehilceListing_OutofService_Period": "Period in Out of service", "TrafficFines": {"trafficFines": "Traffic Fines", "home": "Home", "createFine": "Create Fine", "noFines": "No fines available", "searchPlaceholder": "Search by booking no, plate no, ticket no, or agreement no", "columns": {"violationNo": "Violation No.", "plateNo": "Plate No.", "fineDate": "Fine Date", "chargeStatus": "Charge Status", "paymentStatus": "Payment Status", "bookingNo": "Booking No.", "agreementNo": "Agreement No.", "issuedBranch": "Fine Issue Branch", "amount": "Amount"}, "statuses": {"DRIVER": "Driver", "DEBTOR": "Debtor", "UNPAID": "Unpaid", "PAID": "Paid"}, "filters": {"bookingNo": "Booking No.", "plateNo": "Plate No.", "ticketNo": "Ticket No.", "agreementNo": "Agreement No.", "location": "Location", "chargeStatus": "Charge Status", "paymentStatus": "Payment Status", "dateRange": "Date Range"}, "actions": {"actions": "Actions", "printInvoice": "Print Invoice", "createPaymentLink": "Create Payment Link", "payFromDeposit": "Pay from Deposit", "viewPaymentLink": "View Payment Link"}, "securityDeposit": {"toastTitle": "Deposit does not cover the fine", "toastDescription": "The deposit amount doesn’t not cover the fine amount.", "withdrawFromDeposit": "Withdraw from deposit", "amountInDeposit": "Amount in deposit", "fineAmount": "Fine amount", "approvalCode": "Approval Code", "pos": "POS", "select": "Select", "cardLast4Digits": "Card last 4 digits", "last4Digits": "Last 4 digits", "customerConsent": "I have taken the customer's consent", "cancel": "Cancel", "approvalCodeRequired": "Approval code is required", "cardLast4DigitsRequired": "Last 4 digits are required", "posMachineRequired": "POS machine is required"}, "invoice": {"notFound": "Invoice not found", "checkInvoiceNo": "Check the invoice number and try again", "downloadFailed": "Download Failed", "failedToDownloadInvoice": "Failed to download invoice", "printFailed": "Print Failed", "failedToOpenPrintWindow": "Failed to open print window"}, "paymentLink": {"paymentRequested": "Traffic invoice payment requested", "customerWillReceiveLink": "The customer will receive a link for QuickPay to complete the process", "quickPayLink": "QuickPay link", "paymentTotal": "Payment total", "sar": "SAR", "backToInvoices": "Back to invoices"}, "createTrafficFine": {"recordFine": "Record Traffic Fine", "recordFineDescription": "Record a new traffic fine", "trafficTicketDetails": "Traffic Ticket Details", "ticketNo": "Ticket Number", "enterTicketNo": "Enter ticket number", "licenseNo": "Plate number", "enterLicenseNo": "Enter vehicle's plate number", "violationDate": "Violation Date", "time": "Time", "violationDateAndTime": "Violation Date & Time", "amount": "Amount SAR", "enterAmount": "Enter amount", "cancel": "Cancel", "getAgreementDetails": "Get Agreement Details", "agreementDetails": "Agreement Details", "chargeStatus": "Charge Status", "invoiceToDriver": "Invoice to Driver", "municipality": "Municipality", "selectMunicipality": "Select Municipality", "violationCode": "Violation Code", "selectViolationCode": "Select Violation Code", "agreementNo": "Agreement Number", "status": "Status", "driver": "Driver", "mobileNo": "Mobile No.", "address": "Address", "checkInBranch": "Check In Branch", "createTrafficInvoice": "Create Traffic Invoice", "loadingDetails": "Details are being fetched...", "dontCloseOrRefresh": "Don’t close or refresh this screen", "loading": "Loading...", "errors": {"failedToFetchAgreementDetails": "Failed to fetch agreement details", "ticketNumberRequired": "Ticket number is required", "fineCreatedForThisTicket": "Fine already created for this ticket", "plateNoRequired": "Plate number is required", "violationDateRequired": "Violation date is required", "timeRequired": "Time is required", "amountRequired": "Amount is required", "municipalityRequired": "Municipality is required", "violationCodeRequired": "Violation code is required"}, "dropoffDateTime": "Drop-off Date & Time", "Traffic Invoice created successfully": "Traffic Invoice created successfully", "A copy of the invoice will be sent to the customer’s email address": "A copy of the invoice will be sent to the customer’s email address"}}, "AllBookings": {"title": "All Bookings", "all-bookings": "All Bookings", "refund-requests": "Refund Requests", "emptyMessage": "There are no bookings.", "search": "Search", "columns": {"bookingNo": "Booking No.", "agreementNo": "Agreement No.", "pickupDateTime": "Pick-up date", "dropOffDateTime": "Drop-off date", "pickupBranch": "Pick-up Branch", "dropOffBranch": "Drop-off Branch", "status": "Status", "totalPrice": "Price", "driver": "Driver", "vehicle": "Vehicle", "total": "Total"}, "all-agreements": "All Agreements"}, "Refund": {"requests": "Refund Requests", "columns": {"bookingNo": "Booking No.", "amount": "Amount", "agreementNo": "Agreement No.", "pickupBranch": "Pick-up Branch", "dropOffBranch": "Drop-off Branch", "pickupDateTime": "Pick-up date", "dropOffDateTime": "Drop-off date", "refundAmount": "Refund (SAR)", "iban": "IBAN"}, "emptyMessage": "There are no refund requests.", "search": "Search", "recordRefund": "Record refund", "resendIBANLink": "Resend IBAN link", "resending": "Sending...", "copyLink": "Copy link", "copied": "Copied!", "errors": {"failedToFetchRefundRequests": "Failed to fetch refund requests"}, "toBeRefunded": "To be refunded", "refunded": "Refunded", "copyIbanLink": "Copy IBAN link", "issueRefund": "Issue refund", "amountToRefund": "Amount to refund", "refundMethod": "Refund method", "cash": "Cash", "bankTransfer": "Bank transfer", "ibanLinkSentToCustomer": "IBAN link will be sent to the customer", "customerWillReceiveLink": "The customer will receive a link in the customer's registered mobile number to enter IBAN details to finalize the refund process", "cancel": "Cancel", "processRefund": "Process refund", "requestRefund": "Request refund", "success": "Refund processed", "error": "Error", "refundRequestCreated": "Refund request created successfully", "refundProcessed": "Refund processed successfully"}, "Filters": {"pickupBranch": "Pick-up Branch", "dropOffBranch": "Drop-off Branch", "dropOffTime": "Drop-off Time", "pickupTime": "Pickup Time", "status": "Status", "ibanStatus": "IBAN Status", "values": {"filled": "Filled", "notFilled": "Not Filled", "upcoming": "Upcoming", "no-show": "No Show", "ongoing": "Ongoing", "late-return": "Late Return", "cancelled": "Cancelled", "suspended": "Suspended", "completed": "Completed", "timeFilter": {"all": "All Time"}}, "Search": "Search"}, "CashRegister": {"title": "Cash Register", "closeRegister": "Close Register", "confirmDepositAndCloseRegister": "Confirm deposit and Close Register", "closeCashRegister": "Close Cash Register", "closeCashRegisterDescription": "Are you sure you want to close the cash register?", "confirmDeposit": "Confirm your deposit", "pleaseSelectABankFirst": "Please select a bank first", "nameOfBank": "Name of bank", "selectABank": "Select a bank", "statuses": {"OPEN": "Open", "PRE_CLOSED": "Pre-Closed", "CLOSED": "Closed"}, "openTime": "Open Time", "closeTime": "Close Time", "closedBy": "Closed By", "approvedBy": "Approved By", "columns": {"item": "<PERSON><PERSON>", "cash": "Cash", "pos": "POS", "bankTransfer": "Bank Transfer", "total": "Total"}, "rows": {"openingBalance": "Opening Balance", "received": "Received", "withdraw": "Withdraw", "bankDeposit": "Bank Deposit", "closingBalance": "Closing Balance"}, "actions": {"moveFullBalanceToNextDayAndCloseRegister": "Move full balance to next day & close register", "createDepositRecord": "Create a deposit record", "confirmDepositAndCloseRegister": "Confirm deposit & Close register", "cancel": "Cancel", "confirm": "Confirm"}, "cashBalancePresent": "Cash Balance Present", "cashBalancePresentDescription": "You have a cash balance of SAR {totalBalance}", "amountToDeposit": "Amount to deposit", "remainingBalance": "Remaining (Moved to next day balance)", "toast": {"deposit": {"success": {"title": "Success", "description": "Cash deposit recorded successfully for register #${displayRegisterNo}"}, "error": {"title": "Failed to record deposit, please try again", "description": "Failed to record deposit for register #${displayRegisterNo}"}, "confirm": {"title": "Register closed successfully", "description": "The deposit has been confirmed and register #${displayRegisterNo} is now closed"}}, "close": {"success": {"title": "Success", "description": "Cash register #${displayRegisterNo} closed successfully"}, "error": {"title": "Failed to close register, please try again", "description": "Failed to close register #${displayRegisterNo}"}}, "failedToCloseRegister": "Failed to close register"}, "noCashRegisterDataAvailable": "No cash register data available"}, "invoice": {"title": "Invoice", "invoiceNo": "Invoice No.", "date": "Date", "amount": "Amount", "status": "Status", "paymentMethod": "Payment Method", "paymentStatus": "Payment Status", "actions": {"viewDetails": "View Details", "downloadInvoice": "Download Invoice"}, "statuses": {"PAID": "Paid", "UNPAID": "Unpaid"}, "results": {"success-title": "Agreement closed successfully", "success-description": "A copy of the rental agreement has been sent to the customer's email", "pending-title": "Awaiting <PERSON><PERSON><PERSON> closure", "pending-description": "The invoice is being processed. Please wait...", "error-description": "The invoice generation failed. Please try again.", "error-paragraph": "The system will automatically retry with <PERSON><PERSON><PERSON> and move the booking to the Needs Action tab. The vehicle status will update once <PERSON><PERSON><PERSON> is closed.", "authorization": "authorization", "toast": {"retry": {"success": {"title": "Retry successful", "description": "Authorization retry was successful."}, "failed": {"title": "Retry failed", "description": "An error occurred during retry."}, "error": {"title": "Error", "description": "An error occurred while retrying authorization."}}, "status": {"success": {"title": "Success", "description": "The invoice has been generated successfully."}, "failed": {"title": "Invoice couldn't generate", "description": "The invoice generation failed. Please try again."}, "error": {"title": "Invoice couldn't generate", "description": "An error occurred while generating the invoice."}, "exception": {"title": "Error", "description": "An error occurred while pulling invoice status."}}, "resend": {"success": {"title": "Invoice resent successfully", "description": "The invoice has been resent to ZATCA."}, "failed": {"title": "Failed", "description": "Failed to resend invoice to ZATCA. Please try again."}, "error": {"title": "Error", "description": "An error occurred while sending to ZATCA."}}, "view": {"failed": {"title": "Failed", "description": "Failed to view invoice. Please try again."}, "error": {"title": "Error", "description": "An error occurred while sending to ZATCA."}}, "credit_note": {"success": {"title": "Credit note created successfully", "description": "The credit note has been created successfully."}, "failed": {"title": "Failed to create credit note", "description": "An error occurred while creating the credit note."}, "error": {"title": "Error", "description": "An error occurred while creating the credit note."}}, "debit_note": {"success": {"title": "Debit note created successfully", "description": "The debit note has been created successfully."}, "failed": {"title": "Failed to create debit note", "description": "An error occurred while creating the debit note."}, "error": {"title": "Error", "description": "An error occurred while creating the debit note."}}}, "retry": "Retry", "try-again": "Try again", "retrying": "Retrying...", "reason": "Reason", "invoice-status-pending": "Generating invoice", "invoice-status-error": "Re-generate invoice", "invoice-status-success": "Print invoice", "cta-success": "Back to bookings"}, "list": {"columns": {"invoiceNumber": "Invoice no.", "issueDate": "Invoice Date", "invoiceConfigType": "Invoice Type", "payStatus": "Status", "bookingNumber": "Booking no.", "agreementNumber": "Agreement No.", "branchName": "Invoice issue branch", "totalInvoiceAfterVat": "Amount (SAR)", "totalAmountPaid": "Total Balance ", "invoiceStatus": "Zatca status"}, "emptyMessage": "There are no invoices", "searchPlaceholder": "Search by invoice no, agreement no, booking no.", "filters": {"paymentStatus": {"title": "Payment Status", "paid": "Paid", "unpaid": "Unpaid"}, "invoiceStatuses": {"title": "Zatca Status", "pending": "Pending", "success": "Success", "error": "Failed", "no_zatca": "No Zatca", "cancelled": "Cancelled"}, "invoiceConfigTypes": {"title": "Invoice Type", "driver_invoice": "Driver", "combination": "Debtor", "pre_billing": "Pre-billing", "credit": "Credit note", "debit": "Debit note", "traffic_fine": "Traffic fine", "damage": "Damage", "cancellation": "Cancellation credit note"}, "search": {"bookingNumbers": "Booking No.", "agreementNumbers": "Agreement No.", "invoiceNumbers": "Invoice No."}, "branch": {"title": "Branch"}}, "stats": {"totalAmount": "Total Amount", "totalAmountDue": "Total Balance Due"}, "actions": {"cancel": "Cancel Invoice", "resend": "Resend to ZATCA", "print": "Print Invoice", "create": "Create invoice", "createDebitNote": "Debit Note", "createCreditNote": "Credit Note"}, "title": "Invoices", "toast": {"noteCreation": {"creditNote_creation_success": {"title": "Success", "description": "Credit note created successfully."}, "debitNote_creation_success": {"title": "Success", "description": "Debit note created successfully."}, "creation_error": {"title": "Error", "description": "Failed to create credit note. Please try again."}}}, "pageTitle": "Invoices List"}, "creditDebitNote": {"issueDate": "Invoice Issue Date", "originalInvoiceNumber": {"label": "Original Invoice Number", "placeholder": "Enter original invoice number"}, "agreementNumber": {"label": "Agreement Number", "placeholder": "Agreement number will appear here"}, "reason": {"label": "Reason", "placeholder": "Choose reason"}, "remarks": {"label": "Remarks (optional)", "placeholder": "Enter your remarks here"}, "financial_details": "Financial Details", "credit_note_details": "Credit Note Details", "debit_note_details": "Debit Note Details", "credit_note": "Credit Note", "debit_note": "Debit Note", "creditedAmountBeforeVAT": {"label": "Credited Amount Before VAT", "placeholder": "Enter amount"}, "vatPercentage": "VAT Percentage %", "vatAmount": "VAT Amount", "totalCredit": "Total Credit", "success_credit_cta": "Create Credit Note", "success_debit_cta": "Create Debit Note", "cancel_cta": "Cancel", "cancel_credit_description": "This will cancel the invoice and create a credit note.", "cancel_debit_description": "This will cancel the invoice and create a debit note.", "new_credit_note": "Open new credit note", "new_debit_note": "Open new debit note", "pageTitle": "Credit/Debit Notes List"}, "card": {"title": "View Invoices", "caption": "Booking invoices", "description": "All invoices under this booking will show up here", "simplified": "Simplified TAX Invoice", "noInvoice": "No invoices found for this booking.", "cta": {"viewMore": "View More", "close": "Close"}, "toast": {"failed": {"title": "Failed to fetch invoices", "description": "Unable to fetch invoices for the booking."}, "error": {"title": "Error", "description": "An error occurred while fetching invoices."}}}}, "NRM": {"title": "My Vehicles", "searchPlaceholder": "Search by plate no", "emptyMessage": "No vehicles found", "newNRM": "New NRM", "loadingVehicleDetails": "Loading vehicle details...", "noVehicleDetailsAvailable": "No vehicle details available", "tabs": {"readyForRental": "Ready for Rental", "rented": "Rented", "needsPrep": "Needs Prep", "nrms": "NRMs", "outOfService": "Out of Service"}, "filters": {"location": "Location", "model": "Model", "group": "Group", "reason": "Reason", "from": "From", "to": "To"}, "columns": {"plateNo": "Plate No.", "group": "Group", "location": "Location", "vehicle": "Vehicle", "status": "Status", "fuel": "Fuel", "km": "KM", "actions": "Actions", "bookingNo": "Booking No.", "checkInBranch": "Check-in Branch", "checkInDate": "Check-in Date", "reason": "Reason", "waitingPeriod": "Waiting Period", "nrmNo": "NRM No.", "from": "From", "to": "To", "model": "Model", "driver": "Driver", "nrmStartTime": "NRM Start Time", "nrmCloseTime": "NRM Close Time"}, "actions": {"openNRM": "Open NRM", "moveToService": "Move to out of service", "cancel": "Cancel", "processing": "Processing...", "moveToAvailable": "Move to Ready for Rental", "moveVehicle": "Move vehicle", "closeNRM": "Close NRM"}, "mileage": "Mileage", "fuelLevel": "Fuel Level", "checkoutDetails": "Check out Details", "driverName": "Driver Name (Optional)", "searchByDriverName": "Search by driver name", "reason": "Reason", "selectReason": "Select reason", "moveTo": "Move to", "selectLocation": "Select location", "checkoutTime": "Checkout time", "remarks": "Remarks (optional)", "remarksPlaceholder": "Enter remarks if you have any", "loadingBranches": "Loading branches...", "loadingReasons": "Loading reasons...", "alertTitle": "Vehicle will be moved to out of service", "alertDescription": "The vehicle will automatically be moved to Out of Service for being at work shop.", "OutOfServiceDialog": {"title": "Move to out of service", "description": "Choose reason to move vehicle to out of service", "reason": "Reason for out of service", "chooseReason": "Choose reason for out of service"}, "fuelLevels": {"1": "Quarter", "2": "Half", "3": "Three-Quarters", "4": "Full"}, "available": {"title": "Move to ٍReady for Rental", "description": "Moving the vehicle to available will make it available to be assigned to drivers, are you sure you want to move the vehicle?", "toast": {"success": {"title": "Success", "description": "Vehicle moved to available successfully"}, "error": {"title": "Error", "description": "Failed to move vehicle to available"}}}, "closeDialog": {"title": "Close NRM #{nrmId}", "checkoutDetails": "Check out Details", "driverName": "Driver Name", "searchByName": "Search by name", "reason": "Reason*", "moveTo": "Move to*", "checkoutTime": "Checkout time", "checkinDetails": "Check in Details", "kmIn": "KM In", "kmInPlaceholder": "e.g. 123,800", "fuelIn": "Fuel In", "loadingFuelLevels": "Loading fuel levels...", "selectFuelLevel": "Select fuel level", "highMileageWarning": "KM in seems higher than usual. Please confirm.", "lowMileageError": "KM in cannot be less than current odometer reading ({reading})", "remarks": "Remarks (optional)", "remarksPlaceholder": "Enter a remark if you have any", "cancel": "Cancel", "closeNrm": "Close NRM", "success": {"title": "NRM closed successfully"}, "error": {"title": "Error closing NRM"}}}, "timestamp": {"Today": "Today", "Tomorrow": "Tomorrow", "NOW": "NOW", "1 day passed": "1 day passed", "in {minutes} minutes": "in {minutes} minutes", "in 1 hour": "in 1 hour", "in {hours} hours": "in {hours} hours", "{days} days passed": "{days} days passed"}, "waitingTime": {"days": "days", "hours": "hours", "minutes": "minutes", "weeks": "weeks", "day": "day", "hour": "hour", "minute": "minute", "week": "week"}, "sidebar": {"branch": "Branch", "myBookings": "Branch Bookings", "vehicles": "Branch vehicles", "branchSettings": "Branch settings", "financials": "Financials", "cashRegister": "Cash Register", "payments": "Payments", "refunds": "Refunds", "invoices": "Invoices", "rewards": "Rewards", "quickPay": "Quick Pay", "categories": "Categories", "make": "Make", "model": "Model", "vehicleSpecs": "Vehicle Specs", "customers": "Customers", "drivers": "Drivers", "partners": "Partners", "allReservations": "All Reservations", "reservations": "Reservations", "location": "Location", "cities": "Cities", "branchesYards": "Branches & Yards", "tariff": "Tariff", "b2c": "B2C", "b2b": "B2B", "promotions": "Promotions", "allBookings": "All Bookings", "adminFinancials": "Admin Financials", "allPayments": "All Payments", "allRefunds": "All Refunds", "addons": "Addons", "allBookingsAndAgreements": "All Bookings & Agreements", "allAgreementsdminFinancials": "Admin Financials", "allVehicles": "All Vehicles"}, "branches": {"columns": {"name": "Name", "code": "Code", "type": "Type", "city": "City", "region": "Region", "longitude": "Longitude", "latitude": "Latitude", "location": "Location"}}, "customer": {"columns": {"customerID": "Customer ID", "customerName": "Customer Name", "mobileNumber": "Mobile No.", "email": "Email ID", "driverCode": "Driver", "emailVerified": "<PERSON><PERSON>", "mobileVerified": "Mobile Verified", "profileCompleted": "Profile Completed"}, "pageTitle": "Customers", "searchPlaceholder": "Search by Customer name, mobile or email", "emptyMessage": "There are no Customer Info.", "customerProfile": {"pageTitle": "Customer", "label": {"title": "Title", "firstName": "First Name", "lastName": "Last Name", "mobileNumber": "Mobile Number", "email": "Email ID", "dateOfBirth": "Date Of Birth", "profileCompleted": "Profile Completed", "createdOn": "Created On", "updatedOn": "Updated On", "driverCode": "Driver Code", "customer": "Customer", "language": "Language"}, "heading": {"basicInfo": "Basic Info", "driverInfo": "Driver Info"}}, "navs": {"customerProfile": "Customer Profile", "reservations": "Reservations", "notifications": "Notifications", "agreements": "Agreements"}, "reservations": {"columns": {"referenceNo": "Reference Number", "providerReferenceNo": "Carpro Reservation Number", "rentalSum": "Rental sum", "totalPrice": "Total price", "status": "Booking status", "paymentType": "Payment type", "bookingDateTime": "Booking date", "pickupDateTime": "Pick up time", "dropOffDateTime": "Drop off time"}, "searchPlaceholder": "Search by reference or carpro reservation number", "emptyMessage": "There are no customer reservations."}, "notifications": {"columns": {"id": "ID", "recipient": "Recipient", "status": "Status", "type": "Type", "createdOn": "Created on", "message": "Message", "response": "Response"}, "searchPlaceholder": "Search by id or recipient", "emptyMessage": "There are no customer notifications."}, "agreements": {"columns": {"agreementNo": "Agreement no", "reservationNo": "Reservation no", "agreementStatus": "Agreement status", "licenseNo": "License no", "totalAmount": "Total amount", "totalPaid": "Total paid", "totalBalance": "Total balance"}, "searchPlaceholder": "Search by id or recipient", "emptyMessage": "There are no customer agreements."}}, "createAgreement": {"pageTitle": "Create Agreement", "bookingDetails": {"pageTitle": "Booking Details", "bookingOn": "Booking on", "pickup": "Pickup", "today": "Today", "now": "Now", "orignalPickupTime": "Original Pickup Time", "dropoff": "Drop-off", "discount": "Discount", "preferences": "Customer preferences", "preferencesDesc": "You can edit them in next steps", "group": "Group", "paymentsRefunds": "Payments & Refunds", "noPayments": "There are no transactions!", "loayltyProgramTitle": "Loyalty Program", "loayltyProgram": "Loyalty program", "accountNumber": "Account number", "points": "Points/Miles"}, "assignVehicle": {"pageTitle": "Assign a vehicle", "selectedVehicle": "Selected vehicle", "suggestedVehicles": "Suggested Vehicles", "allBranchesVehicles": "All branch vehicles", "allReadyVehicles": "All ready vehicles", "viewallReadyVehicles": "View all ready vehicles", "cta": {"select": "Select", "upgrade": "Upgrade options", "downgrade": "Downgrade", "cancel": "Cancel", "replace": "Replace vehicle", "freeUpgrade": "Free upgrade", "confirmDowngrade": "Confirm downgrade", "resetFilters": "Reset filters", "viewAll": "View all branch vehicles", "change": "Change Vehicle"}, "noResults": "No results found", "noResultsDesc": "Try modifying or resetting the filters", "replaceModal": {"title": "Replace the selected vehicle?", "description": "The selected vehicle will be removed and replaced with the new one"}, "downgradeModal": {"title": "Confirm vehicle downgrade", "description": "Changing to a downgraded vehicle may require refunding the customer"}, "upgradeModel": {"title": "Upgrade to", "description": "Select the type of upgrade", "reasonForUpgrade": "Reason for upgrade", "approvalText": "I got approval from the branch supervisor/manager", "paidUpgrade": "Paid upgrade", "freeupgrade": "Free upgrade", "successCta": "Upgrade", "negativeCta": "Cancel"}, "vehicleChange": {"title": "Vehicle change notice", "description": "The following item(s) do not apply to the selected vehicle", "cta": {"submit": "Confirm"}}}, "payment": {"discount": {"title": "Discounts", "dialogTitle": "Corporate discounts", "dialogDesc": "Current discount will be lost if you apply new", "cta": {"corporateDiscount": "Corporate discount", "availableDiscount": "Available discounts", "issueRefund": "Issue Refund", "collectPayment": "Collect Payment"}, "editProfile": "Edit profile", "editProfileDesc": "Make changes to your profile here. Click save when you are done.", "payment": "Payment", "noPayments": "There are no payments!"}, "refund": {"title": "Issue refund", "amountToRefund": "Amount to refund", "refundMethod": "Refund method", "cash": "Cash", "bankTransfer": "Bank transfer", "IbanAlertTitle": "IBAN link will be sent to the customer", "IbanAlertDesc": "The customer will receive a link in the customer&apos;s registered mobile number to enter IBAN details to finalize the refund process", "cta": {"negative": "Cancel", "processRefund": "Process Refund", "requestRefund": "Request refund"}}}, "rentalAgreement": "Create rental agreement", "Create rental agreement": "Create rental agreement"}, "bookingDetail": {"cta": {"extend": "Extend Booking", "close": "Close Agreement", "start": "Start Agreement", "edit": "Edit Booking"}, "cancelBy": "Booking canceled by", "assignedVehicle": "Assigned Vehicle", "inspectionReport": "Inspection Report", "discount": "Discount", "reason": "Reason", "Booking": "Booking", "on": "on", "amount": "Amount", "paymentLinks": "Payment Links", "currency": "SAR", "createdOn": "Created On", "trafficFine": "Traffic Fine", "agreementExtension": "Extension", "type": "Type", "Paid": "Paid", "link": "Link", "status": "Status", "active": "Active", "expired": "Expired"}, "fleetManagement": {"title": "Fleet Management", "description": "Manage your fleet of vehicles", "vehicles": {"title": "Vehicles", "available": "Available Vehicles", "inService": "In Service", "outOfService": "Out of Service", "maintenance": "Maintenance", "cleaning": "Cleaning", "fueling": "Fueling"}, "reports": {"title": "Reports", "utilization": "Utilization Report", "maintenance": "Maintenance Report", "fuel": "Fuel Report", "damage": "Damage Report"}, "maintenance": {"title": "Maintenance", "scheduled": "Scheduled Maintenance", "history": "Maintenance History", "upcoming": "Upcoming Maintenance"}, "inventory": {"title": "Inventory", "totalVehicles": "Total Vehicles", "activeVehicles": "Active Vehicles", "inactiveVehicles": "Inactive Vehicles"}, "all-vehicles": {"searchPlaceholder": "Search by plate no", "emptyMessage": "No vehicles found", "column": {"plateNo": "Plate No.", "group": "Group", "vehicle": "Vehicle", "category": "Category", "statusAndSubStatus": "Status & Sub Status", "location": "Location", "serviceType": "Service Type"}, "filters": {"serviceType": "Service Type", "location": "Location", "group": "Group", "status": "Status", "subStatus": "Sub Status", "category": "Category", "vehicle": "Vehicle", "model": "Model"}, "tabs": {"overview": "Overview", "details": "Details", "condition": "Condition", "history": "History", "documents": "Documents"}}}, "cancelBooking": {"title": "Cancel booking", "description": "The booking will be marked as cancelled", "reason_field": "Reason for cancellation", "reason_field_placeholder": "Enter reason", "reasons": {"reason_1": "No available vehicles", "reason_2": "Suspicious customer", "reason_3": "Rejected from Tajeer/Tamm", "reason_4": "Didn’t show a credit card", "reason_5": "Customer was not able to pay", "reason_6": "Other"}, "toast": {"success": {"title": "Booking cancelled successfully", "description": "Booking canceled successfully"}, "failed": {"title": "Failed", "description": "Failed to cancel booking"}, "error": {"title": "Error", "description": "An unexpected error occurred"}}, "cta": {"cancel": "Cancel", "submit": "Cancel booking", "viewMore": "View booking details", "back": "Back to bookings"}, "modalTitle": "Booking Canceled", "modalDesc": "The booking was successfully canceled", "inventory": {"title": "Inventory", "totalVehicles": "Total Vehicles", "activeVehicles": "Active Vehicles", "inactiveVehicles": "Inactive Vehicles"}, "all-vehicles": {"searchPlaceholder": "Search by plate no", "emptyMessage": "No vehicles found", "column": {"plateNo": "Plate No.", "group": "Group", "vehicle": "Vehicle", "category": "Category", "statusAndSubStatus": "Status & Sub Status", "location": "Location", "serviceType": "Service Type"}, "filters": {"serviceType": "Service Type", "location": "Location", "group": "Group", "status": "Status", "subStatus": "Sub Status", "category": "Category", "vehicle": "Vehicle", "model": "Model"}, "tabs": {"overview": "Overview", "details": "Details", "condition": "Condition", "history": "History", "documents": "Documents"}}, "reports": {"title": "Reports", "utilization": "Utilization Report", "maintenance": "Maintenance Report", "fuel": "Fuel Report", "damage": "Damage Report"}, "maintenance": {"title": "Maintenance", "scheduled": "Scheduled Maintenance", "history": "Maintenance History", "upcoming": "Upcoming Maintenance"}, "dialog": {"title": "Cancel booking", "description": "Cancelling booking", "waitingMessage": "Please wait a moment while we create your booking and redirect to the payment page"}, "refund": {"title": "Refund After Cancellation", "description": "If there are any dues, you can refund them after cancellation. Online payments will be refunded automatically within 14 days."}}, "discount": {"search_placehodler": "Search discount code...", "no_search_results": "No results found.", "table": {"company": "Company", "code": "CODE", "amount": "Amount", "discount_value": "Discount Value"}, "remove_discount_title": "Remove discount", "remove_discount_desc": "The discount will be removed from the booking", "cta": {"submit": "Remove", "negative": "Cancel"}, "toast": {"select": {"success": {"title": "Discount added successfully"}}, "remove": {"success": {"title": "Discount removed successfully"}}, "error": "Error applying discount"}, "discount_code": "Discount code", "percentage": "Percentage", "Discount code": "Discount code", "Percentage": "Percentage"}, "driverProfile": {"notAvailable": "N/A", "driverInformation": "Driver information", "nationality": "Nationality", "idNumber": "ID number", "idCode": "ID code", "dateOfBirth": "Date of birth", "age": "Age", "driverLicense": "Driver license", "origin": "Origin", "licenseNumber": "License number", "expiryDate": "Expiry date", "lastBookings": "Last 5 bookings", "upcoming": "Upcoming", "noBookingInformation": "No booking information available", "driverNotFound": "Driver not found", "days": "{count, plural, one {# day} other {# days}}", "noShows": "{count, plural, =0 {No shows} one {# No show} other {# No shows}}", "agreements": "{count, plural, =0 {No agreements} one {# agreement} other {# agreements}}"}, "refundRequest": {"Refund recorded": "Refund recorded", "Refund has been recorded successfully": "Refund has been recorded successfully", "View Booking": "View Booking", "Refund request": "Refund request", "Record refund": "Record refund", "Amount refunded": "Amount refunded", "SAR": "SAR", "Account holder name": "Account holder name", "Enter account holder name": "Enter account holder name", "IBAN Number": "IBAN Number", "Enter IBAN number": "Enter IBAN number", "Bank Name": "Bank Name", "Enter bank name": "Enter bank name", "Remarks": "Remarks", "Enter bank transfer reference number": "Enter bank transfer reference number", "IBAN letter": "IBAN letter", "Upload Document": "Upload Document", "Preview": "Preview", "Cancel": "Cancel", "Error": "Refund Error"}, "debtors": {"title": "Debtors", "create": "Create", "filters": {"service": "Service", "status": "Status", "lease": "Lease", "rental": "Rental", "ucs": "UCS", "commercial": "Commercial", "active": "Active", "inactive": "Inactive", "noRateCard": "No rate card"}, "search": {"placeholder": "Search by debtor name", "noDebtors": "No debtors matching your search"}, "columns": {"debtor": "Debtor", "debtorCode": "Debtor Code", "debtorGroup": "Debtor Group", "service": "Service", "status": "Status", "debtorManager": "<PERSON><PERSON>"}, "edit": "Edit", "modal": {"debtorProfile": "New debtor profile", "debtorGroup": "New debtor group", "deactivateDebtor": "Deactivate debtor?", "deactivateDebtorDesc": "Are you sure you want to deactivate this debtor? Any attached rate cards will also deactivate", "debtorCreated": "Debtor Created", "debtorCreatedDescription": "Debtor has been created successfully! Would you like to create their rate card?", "btn": {"skip": "<PERSON><PERSON>"}}, "btn": {"deactivate": "Deactivate", "deactivating": "Deactivating...", "cancel": "Cancel", "createDebtor": "Create Debtor", "updateDebtor": "Update Debtor"}, "createDebtor": {"title": "Create new debtor", "desc": "Add details for the new debtor"}, "debtorProfile": {"title": "Details", "label": {"debtorName": "Debtor name in English", "debtorNameAr": "Debtor name in Arabic", "debtorGroup": "Debtor group", "vatNumber": "VAT Number", "debtorCode": "Debtor Code", "sapCode": "SAP Code", "companyType": "Company Type", "crNo": "CR Number"}, "placeholder": {"debtorName": "Debtor name", "debtorNameAr": "Debtor name in Arabic", "selectDebtorGroup": "Select debtor group", "vatNumber": "VAT Number", "debtorCode": "Debtor Code", "sapCode": "SAP Code", "companyType": "Select Company Type", "crNo": "CR Number"}, "option": {"clearSelectGroup": "Clear selected group"}}, "address": {"title": "Address", "label": {"shortAddress": "Short Address", "building": "Building #", "street": "Street", "secondary": "Secondary #", "district": "District", "postalCode": "Postal Code", "city": "City"}, "placeholder": {"shortAddress": "Short Address", "building": "Building #", "street": "Street", "secondary": "Secondary #", "district": "District", "postalCode": "Postal Code", "city": "City"}}, "contact": {"title": "Contact Information", "label": {"contactPersonName": "Contact Person Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number"}, "placeholder": {"contactPersonName": "Contact Person Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number"}}, "debtorManager": {"title": "Key Debtor Manager", "label": {"debtorManager": "Key Debtor Manager"}, "placeholder": {"debtorManager": "Key Debtor Manager"}}, "services": {"title": "Services", "label": {"lease": "Lease", "rental": "Rental", "ucs": "UCS", "commercial": "Commercial"}, "modal": {"title": "Deactivate Service?", "desc1": "Are you sure you want to remove this service for the debtor?", "desc2": "Their information is saved and you can reactivate it anytime you prefer."}, "inactivate": "Service is inactive", "reactivate": "Reactivate Service", "inactivateDesc": "This service has been deactivate at {date}. You can click the button below to reactivate this service for the debtor."}, "contract": {"title": "Contract Details", "label": {"contractNumber": "Contract Number", "contractDocument": "Contract document"}, "placeholder": {"contractNumber": "Contract Number"}}, "billingPreferences": {"title": "Billing Preferences", "label": {"billingCycle": "Billing Cycle", "creditLimit": "Credit Limit", "invoiceType": "Invoice Type", "preBilling": "Pre-billing in advance", "endOfMonthBilling": "Bill End of Month"}, "placeholder": {"creditLimit": "4,500"}}, "rateCard": {"title": "Rate Card", "createRateCard": "Create Rate Card", "noRateCard": "You don’t have a rate card for this debtor", "viewDetails": "View Details", "label": {"rateType": "Rate Type", "rateCardName": "Rate card name", "startDate": "Start Date", "endDate": "End Date"}, "placeholder": {"rateType": "Rate Type", "rateCardName": "Rate card name"}, "hasNoCard": "No Rate Card"}, "paymentCoverage": {"title": "Company payment coverage"}, "projects": {"title": "Projects", "label": {"projectID": "Project ID", "projectName": "Project Name"}, "placeholder": {"projectID": "Project ID", "projectName": "Project Name"}, "btn": {"addProject": "Add Project"}, "noProjects": "No projects available."}, "documents": {"uploading": "Uploading...", "title": "Documents", "label": {"taxDocument": "Tax Document", "crDocument": "CR Document"}, "btn": {"otherDocuments": "Upload other documents"}}, "updateDebtor": {"title": "Update debtor"}, "companyType": {"corporate": "Corporate", "government": "Government", "semiGovernment": "Semi Government", "interCompany": "Inter Company"}, "billingCycle": {"30Days": "30 days", "calendarDate": "Calendar date (EOM)"}, "invoiceType": {"separate": "Separate", "consolidated": "Consolidated", "consolidatedByPO": "Consolidated by PO"}}, "booking-details": {"Booking confirmation": "Booking confirmation", "Confirmation email will be sent to the provided address": "A confirmation email will be sent to the provided address", "Confirmation email": "Confirmation email", "Contact email": "Contact email", "Email address": "Email address", "Customer Type": "Customer Type", "Remarks": "Remarks", "Add remarks": "Add remarks", "VIP customer": "VIP customer", "Add remarks here": "Add remarks here", "Attach document": "Attach document", "Uploading": "Uploading...", "Continue": "Continue", "Save changes": "Save changes", "No companies found": "No companies found", "Booking Details": "Booking Details", "Search companies": "Search companies...", "Select codecompany": "Select code/company", "Company/code": "Company/Code", "Debtor PO": "Debtor PO", "Debtor code": "Debtor code", "Vehicle Group": "Vehicle Group", "Authorization Matrix": "Authorization Matrix", "missingFields": "{fields} {fields, plural, one {is} other {are}} missing", "Company payment coverage": "Company payment coverage", "Change company payment coverage": "Change company payment coverage", "No coverage items available": "No coverage items available.", "Edit": "Edit", "Save": "Save", "Cancel": "Cancel", "The changes will apply for the current booking only": "The changes will apply for the current booking only", "Group": "Group", "Debtor": "Debtor"}, "replaceVehicle": {"title": "Replace Vehicle", "agreementNo": "Agreement No", "myBookings": "My bookings", "cta": {"continue": "Continue", "back": "Back", "save": "Save", "exit": "Exit", "submit": "Create agreement"}}}