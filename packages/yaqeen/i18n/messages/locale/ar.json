{"home": {"title": "مرحباً بك في يقين", "description": "هذا وصف تجريبي"}, "LocaleSwitcher": {"ar": "Arabic", "en": "English", "label": "اللغة"}, "nav": {"home": "الرئيسية", "clients": "العملاء", "management": "إدارة الأسطول", "fleet": "الأسطول", "vehicles": "المركبات"}, "booking": {"rentalDuration": {"rental": "قيمة الإيجار", "duration": "لمدة %1$s {days, plural, one {يوم} other {أيام}} {hours, plural, =0 {} other {و %1$s {hours, plural, one {ساعة} other {ساعات}}}}"}, "columns": {"bookingNo": "رق<PERSON> الحجز", "bookingDate": "تاريخ الحجز", "pickupTime": "وقت الاستلام", "dropOffTime": "وقت التسليم", "driver": "السائق", "vehicle": "المركبة", "source": "المصدر", "status": "الحالة", "total": "الإجمالي (ريال)", "na": "<PERSON>ير متاح"}, "actions": {"endAgreement": "إنهاء الاتفاقية", "startAgreement": "بدء الاتفاقية", "viewBooking": "عر<PERSON> الحجز"}, "upcoming": "القادمة", "bookingPrefix": "الحجز #{bookingNumber}", "details": {"bookingDetails": "تفاصيل الحجز", "pickupBranch": "فرع الاستلام", "dropOffBranch": "فرع التسليم", "pickupDate": "تاريخ الاستلام", "dropOffDate": "تاريخ التسليم", "customerDetails": "تفاصيل العميل", "vehicleDetails": "تفاصيل المركبة", "model": "الطراز", "plateNo": "رقم اللوحة", "bookingSummary": "ملخ<PERSON> الحجز"}, "create": {"pageTitle": "إنشاء حجز جديد", "redirecting": "جاري التحويل إلى صفحة تعيين مركبة..."}, "customerTypes": {"individual": "فرد", "corporate": "شركة", "government": "حكومي", "tourist": "سياحي"}, "bookingTypes": {"walkIn": "<PERSON><PERSON><PERSON> مباشر", "online": "عبر الإنترنت", "corporate": "شركة", "government": "حكومي"}, "filters": {"dropOffTime": "وقت التسليم", "pickupTime": "وقت الاستلام", "status": "الحالة", "next2Hours": "خلال ساعتين", "next6Hours": "خلال ٦ ساعات", "today": "اليوم", "next48Hours": "خلال ٤٨ ساعة", "thisWeek": "هذا الأسبوع", "thisMonth": "هذا الشهر", "paid": "مدفوعة", "pending": "قيد الانتظار", "payment": "الدفع", "upcoming": "قادم", "noShow": "ل<PERSON> يحضر", "ongoing": "جاري", "lateReturn": "تأخير في التسليم", "suspended": "معلق", "cancelled": "ملغي", "completed": "مكتمل", "source": "المصدر", "walkIn": "من الفرع", "corporate": "شركة", "online": "اونلاين", "carla": "كارلا", "yolcu": "يولكو"}}, "payment": {"dialog": {"title": "تحصيل الدفع", "collectDeposit": "مبلغ التأمين"}, "amount": {"collect": "المبلغ المراد تحصيله", "placeholder": "أ<PERSON><PERSON><PERSON> المبلغ", "remaining": "الرصيد المتبقي = %1$s %1$s", "deposit": "مبلغ التأمين", "error": "مبلغ غير صالح"}, "payment": {"method": "طريقة الدفع", "cash": "نقدًا", "card": "بطاقة ائتمان / خصم"}, "transaction": {"details": "تفاصيل المعاملة", "manualEntry": "إدخال يدوي", "approvalCode": "رمز الموافقة", "enterApprovalCode": "أدخل رمز الموافقة", "posMachine": "جهاز نقاط البيع", "sendToPos": "إرسال إلى جهاز الدفع", "sendToPosDescription": "إرسال المعاملة إلى جهاز الدفع", "send": "إرسال", "cancel": "إلغاء", "select": "يختار"}, "card": {"last4digits": "آخر 4 أرقام من بطاقة الدفع", "placeholder": "أ<PERSON><PERSON><PERSON> آخر 4 أرقام"}, "button": {"collect": "تحصيل الدفع", "cancel": "إلغاء", "addPayment": "إضافة دفعة", "loading": "جاري معالجة الدفع"}, "toast": {"payment": {"success": "تم تحصيل الدفع بنجاح", "error": "خطأ في تحصيل الدفع"}, "amount": {"error": "مبلغ غير صالح", "exceeds": "تم تحديد مبلغ الدفع بالحد الأقصى: {amount}"}}, "cash": {"collected": "تم تحصيل المبلغ نقدًا"}, "search": {"placeholder": "بحث عن المدفوعات", "noResults": "لم يتم العثور على مدفوعات"}, "download": {"downloadFailed": "فشل التنزيل", "failedToDownloadReceipt": "فشل في تنزيل الإيصال", "printFailed": "فشل الطباعة", "failedToOpenPrintWindow": "فشل في فتح نافذة الطباعة", "bookingDetails": "تفاصيل الحجز", "downloadAgreement": "تنزيل الاتفاقية", "unexpectedError": "حد<PERSON> خطأ غير متوقع", "receiptNotFound": "لم يتم العثور على الإيصال"}, "sidesheet": {"payment_title": "تفاصيل الدفع", "payment_id": "رقم الدفع", "refund_title": "تفاصيل الاسترداد", "record_by": "سُجل بواسطة", "account_holder_name": "اسم صاحب الحساب", "iban_number": "رق<PERSON> الآيبان", "bank_name": "اسم البنك", "iban_letter": "<PERSON>ر<PERSON> ال<PERSON><PERSON><PERSON>", "preview": "معاينة"}, "error": {"amount": {"exceeds": "لا يمكن أن يتجاوز مبلغ الدفع الرصيد المتبقي"}}, "remaining": {"balance": "الرصيد المتبقي = {amount}"}, "receiptNo": "رقم الإيصال", "transactionTime": "وقت المعاملة", "type": "النوع", "status": "الحالة", "actions": "الإجراءات", "viewDetails": "عرض التفاصيل", "amountDetails": {"collect": "المبلغ المطلوب تحصيله", "placeholder": "أ<PERSON><PERSON><PERSON> المبلغ", "remaining": "الرصيد المتبقي = %1$s %1$s", "deposit": "مبلغ التأمين", "error": "مبلغ غير صالح"}, "transactionAmount": "كمية", "CASH": "نقدًا", "Success": "نجاح", "columns": {"Payment details": "تفاصيل الدفع", "View payment details": "عرض تفاصيل الدفع", "Pos": "ج<PERSON>از الدفع", "Online": "عبر الإنترنت", "Open menu": "فتح القائمة", "Success": "نجاح", "Cash": "نقدًا", "Requested": "مطلوب", "Bank transfer": "تحويل بنكي", "pos": "نقطة البيع", "online": "عبر الإنترنت", "success": "نجاح", "cash": "نقدًا", "requested": "مطلوب", "bank_transfer": "تحويل بنكي", "cancelled": "ملغاة"}, "amountCol": "المبلغ"}, "authorization": {"title": "التفويض", "tajeer": {"title": "تفويض تأجير", "description": "إلزامي لجميع المواطنين السعوديين والمقيمين والسياح", "authorized": "مصرح", "failed": "فشل التفويض", "contractNo": "رق<PERSON> عقد التأجير", "viewContract": "عر<PERSON> العقد", "authorizeCustomer": "تفويض العميل", "tryManually": "المحاولة يدويًا", "tryAgain": "المحاولة مرة أخرى", "enterCode": "<PERSON><PERSON><PERSON><PERSON> الرمز", "codeSent": "لقد أرسلنا رمزًا إلى رقم جوال العميل", "validateCode": "التحقق من الرمز", "enterContractNumber": "أد<PERSON><PERSON> رقم عقد تأجير", "openTajeerWebsite": "فتح موقع تأجير", "contractNumberPlaceholder": "رقم عقد تأجير", "submit": "إرسال", "failedTitle": "فشل في تفويض تأجير", "Tajeer skipped": "تجاوز تأجير"}, "tamm": {"title": "تفويض تم", "failedTitle": "فشل في تفويض تم", "description": "إلزامي لعملاء الدراجات والشركات", "authorizeCustomer": "تفويض العميل", "authNumber": "رقم التفويض", "authOn": "تم التفويض في"}, "securityDeposit": {"title": "مبلغ التأمين", "description": "العملاء المشبوهين أو لأول مرة، المركبات الفاخرة", "collectDeposit": "تحصيل التأمين", "collected": "تم التحصيل", "deposit": "إيداع", "Deposit Details": "مبلغ التأمين", "SAR": "ريال", "Success": "نجحت العملية", "Credit card deposit": "إيداع بطاقة الائتمان", "View Details": "عرض التفاصيل", "Amount": "المبلغ", "Authorized On": "تم التفويض", "Receipt number": "رقم الإيصال", "Last 4 digits": "آخر 4 أرقام", "POS": "ج<PERSON>از الدفع", "Collected By": "تم التحصيل بواسطة", "POS machine no": "رقم جهاز الدفع", "withdrawnFromDeposit": "المبلغ المسحوب من الإيداع", "withdrawnAmount": "المبلغ المسحوب", "withdrawnOn": "تم السحب في", "POS machine no.": "رقم جهاز الدفع"}, "otp": {"enterCode": "<PERSON><PERSON><PERSON><PERSON> الرمز", "codeSent": "لقد أرسلنا رمزًا إلى رقم جوال العميل", "expired": "انتهت صلاحية رمز التحقق", "expiredDescription": "انتهت صلاحية رمز التحقق. يرجى المحاولة مرة أخرى.", "resend": "إعادة إرسال رمز التحقق", "resendSuccess": "تم إعادة إرسال رمز التحقق", "resendSuccessDescription": "تم إعادة إرسال رمز التحقق إلى رقم هاتف العميل المحمول", "validateCode": "التحقق من الرمز", "resentTitle": "تم إعادة إرسال رمز التحقق", "resentDescription": "تم إعادة إرسال رمز التحقق إلى رقم جوال العميل", "resendFailedTitle": "فشل في إعادة إرسال رمز التحقق", "resendFailedDescription": "حد<PERSON> خطأ أثناء إعادة إرسال رمز التحقق"}, "success": {"title": "بد<PERSON>ت الاتفاقية بنجاح", "description": "تم إرسال نسخة من اتفاقية الإيجار إلى بريد العميل الإلكتروني", "viewDetails": "عرض تفاصيل الاتفاقية", "backToBookings": "العودة إلى الحجوزات الجارية"}, "errors": {"failedToAuthorize": "فشل في تفويض تأجير", "failedToValidate": "فشل في التحقق من تأجير", "failedToResendOTP": "فشل في إعادة إرسال رمز التحقق", "resendOTPError": "حد<PERSON> خطأ أثناء إعادة إرسال رمز التحقق", "failedToAuthorizeTamm": "فشل في تفويض تم"}, "download": {"downloadFailed": "فشل التنزيل", "failedToDownloadAgreement": "فشل تنزيل الاتفاقية", "printFailed": "فشلت الطباعة", "failedToOpenPrintWindow": "فشل فتح نافذة الطباعة", "bookingDetails": "تفاصيل الحجز", "downloadAgreement": "تنزيل الاتفاقية", "unexpectedError": "حد<PERSON> خطأ غير متوقع", "agreementNotFound": "لم يتم العثور على الاتفاقية"}}, "DataTable": {"RowActions": {"openMenu": "فتح القائمة", "startAgreement": "بدء الاتفاقية", "editBooking": "تعديل الحجز", "cancelBooking": "إلغاء الحجز", "assignVehicle": "تعيين مركبة", "moreDetails": "المزيد من التفاصيل"}}, "bookings": {"columns": {"bookingNo": "رق<PERSON> الحجز", "bookingDate": "تاريخ الحجز", "pickupTime": "وقت الاستلام", "dropOffTime": "وقت التسليم", "driver": "السائق", "vehicle": "المركبة", "source": "المصدر", "status": "الحالة", "total": "الإجمالي (ريال)", "na": "<PERSON>ير متاح", "upcoming": "قادم", "agreementNo": "رقم الاتفاقية #", "group": "المجموعة", "flight": "الرحلة", "dues": "المستحقات (ريال)", "notes": "ملاحظات", "pendingAction": "إجراء معلق", "actions": {"endAgreement": "إنهاء الاتفاقية", "startAgreement": "بدء الاتفاقية", "viewBooking": "عر<PERSON> الحجز"}, "Completed": "مكتمل", "Ongoing": "جاري", "Cancelled": "ملغي", "Upcoming": "قادم", "Paid": "مدفوع", "Unpaid": "غير مدفوع", "Late Return": "تأخير في التسليم", "late return": "تأخير في التسليم", "TAMM Closure Pending": "انتظار إغلاق تم", "TAJEER Extension Pending": "انتظار تمديد تأجير", "TAJEER Pending": "انتظار تأجير", "TAJEER Closure Pending": "انتظار إغلاق تأجير", "TAMM Pending": "انتظار تم", "LATE_RETURN": "تأخير في التسليم", "COMPLETED": "مكتمل", "ONGOING": "جاري", "No dues": "لا توجد مستحقات", "noShow": "ل<PERSON> يحضر", "ongoing": "جاري", "lateReturn": "تأخير في التسليم", "suspended": "معلق", "cancelled": "ملغي", "completed": "مكتمل", "No show": "ل<PERSON> يحضر", "Late return": "تأخير في التسليم", "no show": "ل<PERSON> يحضر"}, "actions": {"endAgreement": "إنهاء الاتفاقية", "startAgreement": "بدء الاتفاقية", "viewBooking": "عر<PERSON> الحجز"}, "upcoming": "القادمة", "bookingPrefix": "الحجز #{bookingNumber}", "details": {"bookingDetails": "تفاصيل الحجز", "pickupBranch": "فرع الاستلام", "dropOffBranch": "فرع التسليم", "pickupDate": "تاريخ الاستلام", "dropOffDate": "تاريخ التسليم", "customerDetails": "تفاصيل العميل", "vehicleDetails": "تفاصيل المركبة", "model": "الطراز", "plateNo": "رقم اللوحة", "bookingSummary": "ملخ<PERSON> الحجز"}, "create": {"pageTitle": "إنشاء حجز جديد", "redirecting": "جاري التحويل إلى صفحة تعيين مركبة..."}, "customerTypes": {"individual": "فرد", "corporate": "شركة", "government": "حكومي", "tourist": "سائح"}, "bookingTypes": {"walkIn": "من الفرع", "online": "اونلاين", "corporate": "شركة", "government": "حكومي"}, "myBookings": "حجوزاتي", "searchPlaceholder": "بح<PERSON> عن الحجوزات", "ongoing": "الجارية", "completed": "المكتملة", "needsAction": "تتطلب إجراء", "allBookings": "جميع الحجوزات", "createBooking": "إنشاء حجز", "filters": {"dropOffTime": "وقت التسليم", "next2Hours": "خلال ساعتين", "next6Hours": "خلال ٦ ساعات", "today": "اليوم", "next48Hours": "خلال ٤٨ ساعة", "thisWeek": "هذا الأسبوع", "thisMonth": "هذا الشهر", "paid": "مدفوعة", "pending": "قيد الانتظار", "payment": "الدفع", "pickupTime": "وقت الاستلام", "status": "الحالة", "upcoming": "قادم", "noShow": "ل<PERSON> يحضر", "ongoing": "جاري", "lateReturn": "تأخير في التسليم", "suspended": "معلق", "cancelled": "ملغي", "completed": "مكتمل", "source": "المصدر", "walkIn": "من الفرع", "corporate": "شركة", "online": "اونلاين", "carla": "كارلا", "yolcu": "يولكو", "lumi": "لومي", "late return": "تأخير في التسليم"}, "searchFilters": {"bookingNo": "رق<PERSON> الحجز", "mobileNumber": "رقم الجوال", "driverName": "اسم السائق", "agreementNo": "رقم الاتفاقية"}, "emptyMessage": "لا توجد أي اتفاقيات", "home": "الرئيسية", "agreements": "الاتفاقيات", "agreement": "الاتفاقية", "on": "في", "cancelled": "ملغي"}, "tariff": {"rentalTariff": "تعرفة الإيجار", "rentalTariffSubTitle": "جميع تعرفات الإيجار المتاحة والتقسيم الأساسي للعام الحالي", "rateCard": "بطاقة الأسعار", "addNewRate": "إضافة سعر جديد", "uploadSheetTemplate": "تحميل قالب الجدول", "useExistingRateCard": "استخدام بطاقة أسعار موجودة", "createNew": "إنشاء جديد", "loadMore": "تحميل المزيد", "noCardsAvailable": "لا توجد بطاقات متاحة", "cancel": "إلغاء", "save": "<PERSON><PERSON><PERSON>", "continue": "متابعة", "delete": "<PERSON><PERSON><PERSON>", "modify": "تعديل", "lastUpdated": "آخر تحديث", "validPeriod": "فترة الصلاحية", "createdBy": "تم الإنشاء بواسطة", "approvalDate": "تاريخ الموافقة", "download": "تنزيل", "downloadAndModify": "تنزيل وتعديل", "stopSales": "إي<PERSON><PERSON><PERSON> المبيعات", "name": "الاسم", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "rates": {"existingRateCard": "بطاقة أسعار موجودة", "existingRateCardDescription": "حدد أي بطاقة أسعار موجودة كأساس لإعداد أسرع"}, "addons": {"addAddOnsRatesBtn": "إضافة أسعار الإضافات", "addAddOnsRatesTitle": "إضافة سعر إضافة جديد", "addOnsRates": "أسعار الإضافات", "noAddOns": "لا توجد إضافات.", "addonsIds": "معرفات الإضافات", "applicableGroup": "المجموعة المطبقة", "type": "النوع", "price": "السعر", "priceUnit": "وحدة السعر", "description": "الوصف"}, "debtor": {"addDebtor": "إضافة مدين جديد", "debtorRate": "سعر المدين", "debtorRateSubTitle": "تتبع وإدارة ملفات المدينين بكفاءة والبقاء على اطلاع بمعدلات التعرفة الخاصة بهم.", "newDebtorCard": "بطاقة مدين جديدة", "newDebtorCardSubTitle": "يمكنك إنشاء مدينين متعددين وتحديد معدل الخصم الخاص بهم.", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "debtorCard": "بطاقات المدين", "fixedRate": "سعر ثابت", "fixedRateDescription": "يقدم سعرًا ثابتًا لا يتغير لخدمات أو مجموعات سيارات محددة، مما يضمن تسعيرًا يمكن التنبؤ به.", "dynamicRate": "خصم", "dynamicRateDescription": "يوفر خصومات على مجموعات سيارات محددة، مما يضمن توفير التكاليف لإيجارات معينة.", "baseCardDetails": "تفاصيل البطاقة الأساسية", "debtorDetails": "تفاصيل المدين"}}, "closeAgreement": {"Inspection Report": "تقرير الفحص", "Pickup Inspection": "فحص الاستلام", "Inspector's remarks": "ملاحظات الفاحص", "New Damage": "<PERSON><PERSON><PERSON> جديد", "Dashboard readings": "قراءات لوحة القيادة", "Item": "العنصر", "Pickup": "الاستلام", "Drop-off": "التسليم", "Difference": "الفرق", "Extra Charge (SAR)": "رسوم إضافية (ريال)", "Damages/Penalties": "الأضرار/الغرامات", "Basic insurance selected": "تم اختيار التأمين العادي", "There are no damages/penalties added": "لم يتم إضافة أضرار/غرامات", "Add new damage/penalty": "إضافة ضرر/غرامة جديدة", "success": {"title": "تم إغلاق الاتفاقية بنجاح", "description": "تم إرسال نسخة من اتفاقية الإيجار إلى بريد العميل الإلكتروني", "viewDetails": "عرض تفاصيل الاتفاقية", "backToBookings": "العودة إلى الحجوزات الجارية"}, "Price breakdown": "تفصيل السعر", "SAR": "ريال", "Rental": "الإيجار", "Insurance": "التأمين", "Discount": "الخصم", "Add-ons": "الإضافات", "Drop-off fee": "رسوم التسليم", "VAT": "ضريبة القيمة المضافة", "Total": "الإجمالي", "Paid amount": "المبلغ المدفوع", "Remaining balance": "الر<PERSON>ي<PERSON> المتبقي", "Booking details": "تفاصيل الحجز", "Booked on": "تم الحجز في", "No discount": "لا يو<PERSON>د خصم", "Amount": "المبلغ", "Customer preferences": "تفضيلات العميل", "You can edit them in next steps": "يمكنك تعديلها في الخطوات التالية", "Group": "المجموعة", "NA": "<PERSON>ير متاح", "Severity": "الخطورة", "Amount: SAR": "المبلغ: ريال", "View Report": "عرض التقرير", "View": "<PERSON><PERSON><PERSON>", "Description": "الوصف", "Type": "النوع", "Report": "التقرير", "Payable (SAR)": "المبلغ المستحق (ريال)", "Edit KMs reading": "تعديل قراءة الكيلومترات", "Check-out KMs reading": "قراءة عداد الكيلومترات عند الخروج", "Check-in KMs reading": "قراءة عداد الكيلومترات عند الوصول", "Check-out fuel level": "مستوى الوقود عند الخروج", "Check-in fuel level": "مستوى الوقود عند الوصول", "Extra charge (Including VAT)": "الرسوم الإضافية (شامل ضريبة القيمة المضافة)", "Waive extra charge": "إعفاء من الرسوم الإضافية", "Waive reason": "سبب الإعفاء", "Loyal customer": "عميل مميز", "Complementary waive": "تعويض اعتذاري", "Other reason": "سب<PERSON>ر", "I got approval from the branch supervisor/manager": "حصلت على الموافقة من مشرف/مدير الفرع", "Cancel": "إلغاء", "Comprehensive insurance selected": "تم اختيار التأمين الشامل", "Pay1500": "د<PERSON><PERSON> بحد أقصى 1,500 ريال سعودي مع تقرير الشرطة.", "Apply charge": "تطبيق الرسوم", "Uploading file": "جارٍ تحميل الملف", "Attach police report": "إرفاق تقرير الشرطة", "Low": "طفيف", "Medium": "متوسط", "Major": "كبير", "Penalties & Damages costs": "تكاليف الغرامات والتعويضات", "Payable amount": "المبلغ المستحق (غير ضريبة القيمة المضافة)", "File selected": "تم اختيار الملف", "KMs": "كم", "KM": "كم", "Pay up to": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "with a police report": "مع إرفاق تقرير الشرطة", "cta": {"back": "رجوع", "save": "خروج", "continue": "حفظ ومتابعة", "close": "إغلاق الاتفاقية", "cancel": "إلغاء"}, "dialog_title": "هل تريد إغلاق العقد؟", "dialog_desc": "سيؤدي إغلاق هذا العقد إلى إغلاق تصاريح تأجير أو تم أيضاً، لا يمكنك التراجع عن هذا الإجراء.", "Drop-off Inspection": "فحص التسليم", "trafficFines": {"title": "الغرامات المرورية", "loading": "جارٍ تحميل الغرامات المرورية...", "total": "الإجمالي", "trafficFine": "غرامة مرورية", "unknownViolation": "غير معروف", "paid": "مدفوع", "invoice": {"notFound": "الفاتورة غير موجودة", "checkInvoiceNo": "يرجى التحقق من رقم الفاتورة وإعادة المحاولة مرة أخرى", "downloadFailed": "فشل في التنزيل", "failedToDownloadInvoice": "فشل في تنزيل الفاتورة", "printFailed": "فشل في الطباعة", "failedToOpenPrintWindow": "فشل في فتح نافذة الطباعة"}}, "No inspection details": "لا توجد تفاصيل فحص", "myBookings": "حجوزاتي", "createBooking": "إنشاء حجز", "endAgreement": "إنهاء الاتفاقية", "extendBooking": "<PERSON><PERSON><PERSON><PERSON><PERSON> الحجز", "closeAgreement": "إغلاق اتفاقية الإيجار", "agreementNo": "رقم الاتفاقية", "Edit damage/penalty": "تعديل الضرر/الغرامة", "Add damage/penalty description": "أضف وصف الضرر/الغرامة", "Select severity": "اختر الخطورة", "Enter payable amount here": "أد<PERSON>ل المبلغ المستحق هنا", "Choose police report file": "اختر ملف تقرير الشرطة", "Save changes": "حفظ التغييرات", "Add damage or penalty": "إضافة ضرر/غرامة", "Updating...": "جارٍ التحديث...", "Adding...": "جارٍ الإضافة...", "Error": "خطأ", "Saving...": "جارٍ الحفظ...", "Please provide a damage description": "يرجى تقديم وصف للضرر", "Failed to upload police report file. Please try again.": "فشل في تحميل ملف تقرير الشرطة. يرجى المحاولة مرة أخرى.", "Success": "نجاح", "Damage/penalty has been updated successfully": "تم تحديث الضرر/الغرامة بنجاح", "Damage/penalty has been added successfully": "تمت إضافة الضرر/الغرامة بنجاح", "Failed to update damage/penalty. Please try again.": "فشل في تحديث الضرر/الغرامة. يرجى المحاولة مرة أخرى.", "Failed to add damage/penalty. Please try again.": "فشل في إضافة الضرر/الغرامة. يرجى المحاولة مرة أخرى.", "Fuel Level": "مستوى الوقود", "Inspection details": "تفاصيل الفحص", "Payment": "الدفع", "Authorization": "التفويض", "Saving": "جارٍ الحفظ...", "DAMAGE": "ضرر", "Customer selection": "اختيار العميل", "Edit fuel level": "تعديل مستوى الوقود", "Updating": "جارٍ التحديث...", "Adding": "جارٍ الإضافة...", "Failed to upload police report file Please try again": "فشل في تحميل ملف تقرير الشرطة. يرجى المحاولة مرة أخرى.", "Failed to update damage penalty Please try again": "فشل في تحديث الضرر/الغرامة. يرجى المحاولة مرة أخرى.", "Failed to add damage penalty Please try again": "فشل في إضافة الضرر/الغرامة. يرجى المحاولة مرة أخرى.", "trafficFine": "غرامة مرورية"}, "promotions": {"promotions": "العروض الترويجية", "createCustomizedCampaigns": "إنشاء حملات مخصصة لكل نوع من العملاء", "createNewPromotion": "إنشاء عرض ترويجي جديد", "promotionDetails": "تفاصيل العرض الترويجي", "discount": "الخصم (%)", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "branches": "الفروع", "carGroups": "مجموعات السيارات", "redemptionLimit": "ح<PERSON> الاستخدام", "durationLimit": "<PERSON><PERSON> المدة", "eligibleDays": "الأيام المؤهلة", "paymentOptions": "خيارات الدفع", "payOnline": "دفع أونلاين", "payAtBranch": "الدفع في الفرع", "terms": "الشروط", "cancel": "إلغاء", "save": "<PERSON><PERSON><PERSON>", "day": "الأيام", "voucherType": "نوع القسيمة", "promotionalCampaign": "حملة ترويجية", "corporateCDP": "CDP الشركات", "oneTimePromo": "عرض لمرة واحدة", "promotionalCampaignDescription": "خصم يُمنح لمناسبة معينة مثل خصم البنك أو فعاليات الشركة وما إلى ذلك", "corporateCdpDescription": "عرض مُقدم لموظفي الشركات الشريكة", "oneTimePromoDescription": "قسائم يمكن استردادها فقط للعروض الخاصة", "promotionNameEn": "اسم العرض الترويجي بالإنجليزية", "promotionNameAr": "اسم العرض الترويجي بالعربية", "details": "التفاصيل", "code": "الر<PERSON>ز", "payOnlineDescription": "مثالي للعملاء الذين يفضلون تأكيد حجوزاتهم فورًا بدفع مسبق.", "payAtBranchDescription": "مثالي للذين يفضلون المعاملات الشخصية والمرونة في الفرع.", "promotionalBanner": "التصميم الترويجي", "promotionalBannerDescription": "سيتم استخدام الصورة لتظهر في صفحة العروض التطبيق والموقع الإلكتروني", "selectBranches": "اختر الأفرع", "selectCarGroups": "اختر مجموعات السيارات", "chooseBranches": "اختر الأفرع", "close": "إغلاق", "showInApp": "إظهار الخصم في التطبيق", "showInAppDescription": "حدد ما إذا كان الخصم ظاهراً في تطبيق لومي للجوال أم لا.", "eligibleDaysDescription": "حدد أيام الأسبوع المحددة التي سيكون هذا العرض متاحًا فيها", "validDate": "تاريخ الصلاحية", "termsAndConditions": "الشروط والأحكام", "termsAndConditionsPlaceholder": "اكتب الشروط والأحكام التفصيلية، بما في ذلك مسؤوليات المستخدم وسياسات الخصوصية وإخلاء المسؤولية القانونية.", "promotionName": "اسم الخصم", "redemptionLimitDescription": "<PERSON><PERSON><PERSON> عدد مرات الاستخدام", "numberOfRedemptions": "عدد مرات الاستخدام", "durationLimitDescription": "<PERSON><PERSON><PERSON> الح<PERSON> الأدنى أو الأقصى لمدة الإيجار", "select": "اختر", "selectedBranches": "{count, plural, =0 {لم يتم اختيار أي فرع} =1 {تم اختيار فرع واحد} =2 {تم اختيار فرعين} other {تم اختيار # فروع}}.", "eligibleEmailDomains": "نطاقات البريد الإلكتروني المتاحة", "noOfVouchers": "<PERSON><PERSON><PERSON> القسائم", "vouchersDescription": "سيتم إنشاء قائمة بالرموز بعد إنشاء الخصم", "descriptionEn": "الوصف بالإنجليزية", "descriptionAr": "الوصف بالعربية", "editPromotion": "تعديل الخصم", "editPromotionDescription": "تعديل تفاصيل الخصم", "selectAll": "اختر الكل", "activate": "تفعيل", "deactivate": "إلغاء التفعيل", "branchesCount": "{count, plural, =0 {فروع} =1 {فرع} =2 {فرعان} other {فروع}}.", "edit": "تعديل", "statusTitle": "هل أنت متأكد أنك تريد {status} هذا العرض الترويجي؟", "preview": "معاينة", "remainingCount": "الع<PERSON><PERSON> المتبقي", "eligibleEmailDomainsDescription": "اضغط على Enter أو انقر بعيدًا لإضافة نطاق جديد"}, "vehicle-status": {"title": "حالة المركبة", "description": "اختر حالة المركبة بعد استبدال المركبة", "needs-service": "تحتاج إلى تعبئة وقود أو تنظيف", "workshop": "إرسال إلى الورشة", "ready": "تحديد المركبة كجاهزة"}, "replace-reason": {"title": "سبب الاستبدال", "description": "اختر سبب استبدال المركبة", "vehicle-issue": "عطل أو خلل في المركبة", "vehicle-issue-description": "المركبة تعاني من عطل ميكانيكي أو خلل في النظام.", "accident-damage": "حادث أو ضرر في المركبة", "accident-damage-description": "المركبة تعرضت لحادث أو بها أضرار ظاهرة", "maintenance": "صيانة", "maintenance-description": "المركبة تحتاج إلى صيانة", "customer-unsatisfied": "عدم رضا العميل", "customer-unsatisfied-description": "العميل طلب استبدال المركبة"}, "pricing": {"discount": "الخصم", "priceBreakdown": "تفاصيل السعر", "rentalLabel": "الإيجار", "rentalPeriod": "لمدة {days, plural, =0 {} one {يوم واحد} two {يومان} other {# أيام}} {hours, plural, =0 {} one {و ساعة واحدة} two {و ساعتان} other {و # ساعات}}", "insurance": "التأمين", "addOns": "الإضافات", "dropOffFee": "رسوم التسليم", "vat": "ضريبة القيمة المضافة", "total": "الإجمالي", "paidAmount": "المبلغ المدفوع", "remaining": "المتبقي", "duration": "{days, plural, =0 {0 يوم} one {# يوم} other {# أيام}} {hours, plural, =0 {} one {و # ساعة} other {و # ساعات}}", "errors": {"fetchQuoteDetails": "تعذر استلام تفاصيل العرض (الخطأ: {errorCode})", "failedToLoadPricing": "فشل تحميل معلومات التسعير", "showingPartialInfo": "عرض معلومات جزئية فقط"}, "insurancee": "التأمين", "Discount": "الخصم", "Rental": "التأجير", "SAR": "ريال", "Pickup": "الاستلام", "Drop-off": "التسليم", "days": "أيام", "hours": "ساعة", "Booking summary": "تفاصيل الحجز", "Price breakdown": "تفصيل السعر", "extraFuelCharges": "رسوم الوقود الإضافية", "extraKmCharges": "رسوم الكيلومترات الإضافية", "trafficFine": "غرامة مرورية", "comprehensiveInsurance": "تأمين شامل", "free": "مجاني", "addonFallback": "إضافة {number}", "basicInsurance": "التأمين الأساسي", "damagesPenalties": "الأضرار/العقوبات", "Driver Pays": "يدفع السائق", "Company Pays": "تدفع الشركة"}, "common": {"breadcrumbs": {"home": "الرئيسية", "myBookings": "حجوزات الفرع", "createBooking": "إنشاء اتفاقية جديدة", "All Bookings": "جميع الحجوزات"}, "actions": {"back": "رجوع", "save": "<PERSON><PERSON><PERSON>", "exit": "خروج", "saveAndExit": "خروج", "continue": "متابعة", "saveAndContinue": "حفظ ومتابعة", "assignAndContinue": "تعيين ومتابعة", "createBooking": "متابعة", "createAgreement": "إنشاء العقد", "loading": "جاري التحميل", "export": "تصدير إلى اكسل", "goBack": "العودة", "clear": "م<PERSON><PERSON>", "createBookingDebtor": "إنشاء حجز", "failure": "فشل"}, "errors": {"somethingWentWrong": "حدث خطأ غير متوقع, يرجى إعادة المحاولة.", "genericError": "حدث خطأ غير متوقع, يرجى إعادة المحاولة.", "agreementCreation": "فشل إنشاء عقد الإيجار، يرجى المحاولة مرة أخرى.", "unexpectedError": "حدث خطأ غير متوقع, يرجى إعادة المحاولة.", "fetchQuoteDetails": "تعذر استلام تفاصيل العرض", "failedToFetchBranches": "خلل في عرض الفروع، يرجى المحاولة مرة أخرى.", "failedToFetchBookings": "خلل في عرض الحجوزات، يرجى المحاولة مرة أخرى.", "unauthorized": "غير مصرح بالوصول", "oops": "عذراً!  لدينا خطأ في البرمجة، يرجى إعادة المحاولة.", "dontWorry": "الرجاء المحاولة لاحقاً"}, "toast": {"changesSaved": "تم حفظ التغييرات", "bookingUpdated": "تم تحديث الحجز بنجاح", "bookingCreated": "تم إنشاء الحجز", "bookingCreatedSuccess": "تم إنشاء الحجز بنجاح"}, "bookingCreation": {"creatingBooking": "جاري إنشاء الحجز", "waitingMessage": "يرجى الانتظار لحظة بينما نقوم بإنشاء حجزك وتوجيهك إلى صفحة الدفع"}, "notFound": {"noResults": "لم يتم العثور على نتائج"}, "loading": "جاري التحميل...", "tryAgain": "حاول مرة أخرى", "usermenu": {"switchBranch": "تغيير", "switchBranch2": "تغيير الفرع", "selectBranch": "اختر فرعاً من القائمة", "logout": "تسجيل الخروج", "switchToEnglish": "Switch to English", "switchToArabic": "التبديل إلى اللغة العربية", "Arabic": "العربية", "English": "English", "changeLanguage": "تغيير اللغة"}, "filters": {"selected": "فلاتر محددة", "noResultsFound": "لا توجد نتائج", "clearFilters": "إزالة الفلاتر"}, "pagination": {"showing": "عرض {start}-{end} من {total}", "firstPage": "الصفحة الأولى", "previousPage": "الصفحة السابقة", "nextPage": "الصفحة التالية", "lastPage": "الصفحة الأخيرة", "of": "من"}, "home": "الرئيسية", "welcome": "مرحباً", "View Bookings": "عرض الحجوزات", "Manage your rental bookings": "إدارة حجوزات الإيجار", "Branch Settings": "إعدادات الفرع", "Configure branch settings": "<PERSON><PERSON><PERSON> إعدادات الفرع", "Cash Register": "صندوق الدفع", "View cash register details": "عرض تفاصيل صندوق الدفع", "Select branch": "اختر الفرع", "Enter debtor PO": "أدخل أمر شراء المدين"}, "navigation": {"bookingSteps": {"bookingdetails": "تفاصيل الحجز", "driverdetails": "تفاصيل السائق", "assignavehicle": "تعيين مركبة", "insuranceandextras": "التأمين والإضافات", "payment": "الدفع", "authorization": "التفويض", "additionaldetails": "تفاصيل إضافية"}, "dropOff": "تسليم", "Booking Confirmed": "تم تأكيد الحجز", "The booking has been successfully created": "تم إنشاء الحجز بنجاح", "Total": "الإجمالي", "SAR": "ريال سعودي", "Booking": "<PERSON><PERSON><PERSON>", "pickup": "استلام", "days": "أيام", "hours": "ساعات", "Group": "مجموعة", "Back to my bookings": "العودة إلى حجوزاتي"}, "create": {"pageTitle": "إنشاء حجز جديد", "redirecting": "جاري التحويل إلى صفحة تعيين مركبة..."}, "driver": {"mainDriver": "السائق الرئيسي", "customerId": "رقم هوية العميل", "idNumber": "رخصة قيادة، هوية وطنية سعودية، إقامة، هوية خليجية، أو جواز سفر", "searchPlaceholder": "البحث برقم الوثيقة...", "createNew": "إنشاء سائق جديد", "searching": "جاري البحث عن السائقين...", "noDriversFound": "لم يتم العثور على سائقين."}, "vehicles": {"availableVehicles": "المركبات المتاحة", "readyVehiclesCount": {"zero": "لا توجد مركبات جاهزة", "one": "مركبة جاهزة واحدة", "two": "مركبتان جاهزتان", "few": "", "many": "", "other": "# مركبات جاهزة"}, "allGroups": "جميع المجموعات", "upgradeOptions": "خيارات الترقية", "selecting": "جاري الاختيار", "select": "اختيار", "noResults": "لم يتم العثور على نتائج", "modifyFilters": "حاول تعديل أو إزالة الفلاتر", "viewAllBranchVehicles": "عرض جميع مركبات الفرع", "resetFilters": "إزالة الفلاتر", "vehicleDetails": "تفاصيل المركبة", "viewVehicleDetails": "اعرض تفاصيل المركبة هنا.", "moreDetails": "المزيد من التفاصيل", "km": "كم", "sar": "ريال", "group": "المجموعة", "na": "<PERSON>ير متاح", "allIncluded": "شامل الجميع", "preferenceTypes": {"exactMatch": "تطابق تام", "similar": "مشابهة", "upgrade": "ترقية", "downgrade": "تخفيض", "none": "لا شيء"}, "viewMore": {"prefix": "عرض المزيد من", "similar": "المركبات المشابهة", "upgrades": "الترقيات", "matches": "المركبات المطابقة"}, "tooltips": {"kilometers": "الكيلومترات", "fuel": "الوقود", "inspected": "تم فحصها", "inclusionDetails": "الإيجار والتأمين والإضافات"}, "errors": {"title": "خطأ", "defaultMessage": "<PERSON><PERSON><PERSON>", "vehicleLockFailed": "فشل في حجز المركبة", "pricingFailure": "خطأ في التسعير", "missingQuoteId": "معرف العرض مفقود", "unexpectedError": "حد<PERSON> خطأ غير متوقع"}, "readyVehicles": "مركبات جاهزة", "inspection": {"neverInspected": "لم يتم فحص المركبة مسبقاً", "minutesAgo": "منذ {count} دقيقة", "hoursAgo": "منذ {count} ساعات", "yesterday": "<PERSON><PERSON><PERSON>", "today": "اليوم", "sameWeek": "نفس الأسبوع", "moreThanAWeek": "أكثر من أسبوع", "onDay": "في {day}", "onDate": "في {date}"}, "vehicleStatus": "حالة المركبة", "readyToRent": "جاهزة للإيجار", "viewReport": "عرض التقرير", "features": "المميزات", "upgradeVehicle": "ترقية المركبة", "selectVehicle": "اختيار المركبة", "cleaning": {"neverCleaned": "لم يتم تنظيف المركبة مسبقاً", "minutesAgo": "منذ {count} دقيقة", "hoursAgo": "منذ {count} ساعة", "yesterday": "<PERSON><PERSON><PERSON>", "onDay": "في {day}", "onDate": "في {date}"}, "selectedVehicle": "المركبة المختارة", "changeVehicle": "تغيير المركبة", "noFeatures": "لا توجد ميزات متوفرة", "loadingFeatures": "جارِ التحميل...", "emptyState": {"noReadyVehicles": "لا توجد مركبات جاهزة متاحة في الفرع", "viewAll": "عرض جميع مركبات الفرع", "onlyDowngrade": "تتوفر خيارات التخفيض فقط", "noExactmatch": "لا توجد مركبات مطابقة أو مشابهة متاحة", "customerPreference": "تفضيلات العميل"}, "specifications": {"doors": "الأبواب", "gears": "التروس", "fuelType": "نوع الوقود", "cylinders": "الأسطوانات", "driveType": "نوع الدفع", "engineSize": "حجم المحرك", "horsepower": "ع<PERSON><PERSON> الأحصنة", "convenience": "الراحة", "measurements": "الأبعاد", "transmission": "ناقل الحركة", "seat": "المقعد", "exteriorFeatures": "المميزات الخارجية", "interiorFeatures": "المميزات الداخلية", "bodyType": "نوع الهيكل", "noFeatures": "لا توجد ميزات متوفرة", "fuelCapacity": "سعة الوقود"}, "success": {"vehicleSelected": "تم اختيار المركبة بنجاح"}}, "insurance": {"title": "التأمين", "thirdParty": {"description": "في حالة وقوع حادث، سيتم تحميل حد أقصى قدره {deductible} ريال إذا كنت مخطئاً.", "description_default": "في حالة وقوع حادث، سيتم فرض رسوم الحد الأقصى في حالة الخطأ."}, "pricing": {"withDeductible": "+ ريال {amount}", "free": "مجاني", "perDay": "{amount}/يوم"}}, "extras": {"kmsAllowance": {"title": "الكيلومترات المتاحة", "standardKm": "{km} كم", "unlimitedKm": "كيلومترات غير محدودة"}, "addOns": {"title": "الإضافات", "noExtras": "لا توجد إضافات متاحة"}, "pricing": {"free": "مجاني", "addOn": "+ {currency} {amount}", "sar": "ريال"}, "errors": {"fetchQuoteDetails": "فشل في استلام تفاصيل العرض", "failedToFetch": "فشل في استلام الإضافات"}}, "drivers": {"title": "تفاصيل السائق", "labels": {"mainDriver": "السائق الرئيسي"}, "sections": {"driverDetails": "تفاصيل السائق", "drivingLicense": "رخصة القيادة"}, "fields": {"firstName": "الاسم الأول", "lastName": "اسم العائلة", "mobile": "رقم الجوال", "email": "الب<PERSON>يد الإلكتروني", "nationality": "الجنسية", "idNumber": "رقم الهوية", "number": "رقم", "idType": "نوع الهوية", "dateOfBirth": "تاريخ الميلاد", "dateOfBirthHijri": "تاريخ الميلاد - هجري", "dateOfBirthGregorian": "تاريخ الميلاد - ميلادي", "idExpiryDate": "تاريخ انتهاء الهوية", "idExpiryDateHijri": "تاريخ انتهاء الهوية - هجري", "idExpiryDateGregorian": "تاريخ انتهاء الهوية - ميلادي", "idIssuedCountry": "بلد إصدار الهوية", "address": "العنوان", "country": "البلد", "licenseNumber": "رقم الرخصة", "licenseExpiryDate": "تاريخ انتهاء الرخصة", "licenseExpiryDateHijri": "تاريخ انتهاء الرخصة - هجري", "licenseExpiryDateGregorian": "تاريخ انتهاء الرخصة - ميلادي", "borderNumber": "رقم الحدود", "iqamaNumber": "رقم الإقامة", "iqamaExpiryDate": "تاريخ انتهاء الإقامة", "passportIssueCountry": "بلد إصد<PERSON><PERSON> جواز السفر", "passportNumber": "رقم جواز السفر", "passportExpiryDate": "تاريخ انتهاء صلاحية جواز السفر", "expiryDate": "تاريخ انتهاء الصلاحية"}, "idTypes": {"saudiNational": "الهوية", "gcc": "دول الخليج", "resident": "مقيم", "visitor": "زائر", "passport": "جواز سفر", "iqama": "إقامة", "saudiNational2": "مواطن سعودي"}, "actions": {"createNewDriver": "إضافة سائق جديد", "createDriver": "إضافة سائق", "saveChanges": "حفظ التغييرات", "removeDriver": "إزالة السائق", "removeDriverDescription": "يمكنك اختيار سائق آخر بعد الإزالة.", "cancel": "إلغاء", "remove": "إزالة", "getBorderNumber": "الحصول على رقم الحدود"}, "values": {"na": "<PERSON>ير متاح", "fillIt": "م<PERSON><PERSON><PERSON><PERSON>"}, "search": {"idNumberTitle": "رقم هوية العميل", "idNumberDescription": "رخصة القيادة، الهوية الوطنية، الإقامة، الهوية الخليجية، أو جواز السفر", "placeholder": "البحث برقم الوثيقة...", "searching": "جاري البحث عن السائقين...", "noResults": "لم يتم العثور على سائقين.", "noDriverFound": "لم يتم العثور على السائق"}, "dialogs": {"cancelCreation": {"title": "إلغاء إضافة السائق", "description": "هل أنت متأكد أنك تريد الإلغاء؟ سيتم فقدان جميع المعلومات المدخلة.", "confirm": "نعم، إلغاء", "continue": "متابعة التعديل"}, "cancelEdit": {"title": "إلغاء التغييرات؟", "description": "سيتم تجاهل جميع التغييرات التي قمت بها", "confirm": "إلغاء التغييرات", "continue": "متابعة التعديل"}}, "success": {"driverCreated": "تمت إضافة السائق بنجاح", "driverUpdated": "تم تحديث بيانات السائق بنجاح"}, "errors": {"failed": "فشل في إنشاء السائق", "serverFailure": "خطأ في الخادم", "failedToCreateDriver": "فشل في إنشاء السائق", "failedToUpdateDriver": "فشل في تحديث بيانات السائق", "missingQuoteId": "رقم عرض السعر مفقود", "failedToAssignDriver": "فشل في تعيين السائق", "unexpectedError": "حد<PERSON> خطأ غير متوقع", "driverCreationFailed": "فشل في إنشاء السائق"}}, "datePicker": {"selectMonth": "اخت<PERSON> الشهر", "pickDate": "اختر التاريخ"}, "VehicleListing": {"Assigned": "مخصصة", "Vehicle": "المركبة", "OutofService": "<PERSON>ارج الخدمة", "Location": "الموقع", "ReadyforRental": "جاهزة للتأجير", "MyVehicles": "مركبات الفرع", "OpenNRM": "بدء تحويلة", "NRMs": "التحويلات", "Unassigned": "غير مخصصة", "Rented": {"Reason": {"Maintenance": "صيانة", "Fuel/Cleaning": "التزود بالوقود/التنظيف", "Drop-offLocation": "مكان التسليم", "Drop-offDate&Time": "تاريخ ووقت التسليم", "WorkshopTransfer": "نقل إلى الورشة"}, "TodayDrop-off": "التسليم اليوم", "Outofservicemovement": "نقل إلى خارج الخدمة", "2hoursDropOff": "التسليم خلال ساعتين", "WaitingPeriod": "فترة الانتظار", "PassedTime": "الوقت الذي مضى"}, "NeedsPrep": {"Reason": "السبب", "MovingVehicleDescription": "إن نقل السيارة إلى السيارات المتاحة سيجعلها متاحة للتأجير هل أنت متأكد أنك تريد نقل السيارة؟", "MoveToAvailable": "نقل إلى السيارات المتاحة", "MovingVehicle": "نقل المركبة", "MovementTitle": "نقل إلى السيارات المتاحة"}, "MovingVehicle": {"Description": "اختر سبب نقل السيارة إلى خارج الخدمة.", "OpeningNRM": {"EmployeeID": "رقم الموظف (اختياري)", "CheckoutTime": "وقت الخروج", "RemarkDescription": "أدخل ملاحظة إذا كان لديك", "OutOfServiceDescription": "سيتم نقل السيارة تلقائيًا إلى \"خارج الخدمة\" لوجودها في ورشة العمل.", "OutOfServiceTitle": "سيتم نقل السيارة إلى خارج الخدمة", "OptionalRemarks": "ملاحظات (اختياري)", "MoveTo": "النقل إلى", "CheckoutDetails": "تفاصيل الخروج"}, "ReasonTitle": "سبب الخروج من الخدمة", "MovedSuccessfullyTitle": "تم نقل المركبة بنجاح", "Reason": {"RegistrationExpired": "إنتهاء صلاحية التسجيل", "Operationcardexpired": "إنتهاء صلاحية بطاقة تشغيل المركبة", "InsuranceExpired": "إنتهاء صلاحية التأمين"}, "ReasonDescription": "اختر سبب الخروج من الخدمة", "MovedSuccessfulyDescription": "لقد تم نقل السيارة إلى خارج الخدمة بنجاح.", "dialog": {"title": "نقل المركبة"}}}, "NeedsPrep": "تحتاج تجهيز", "Rented": "مستأجرة", "OpeningNRM": "بدء تحويلة جديدة", "VehicleListing_NRM_CheckInDetails": "تفاصيل الدخول", "VehicleListing_NRM_Error_KMIn_Title": "يبدو أن الكيلومترات أعلى من المعتاد", "VehicleListing_NRM_Success_Title": "تم إغلاق التحويلة بنجاح", "VehicleListing_NRM_Startedin2hours": "بدأت في آخر ساعتين", "VehicleListing_NRM_KMIn": "كيلومترات عند الوصول", "VehicleListing_NRM_Success_Description": "تم إغلاق الحركة الداخلية وتم نقل السيارة إلى خارج الخدمة.", "VehicleListing_NRM_FuelIn": "الوقود عند الوصول", "VehicleListing_NRM_Error_KMIn_Description": "يبدو أن المسافة المقطوعة بالسيارة القادمة أعلى من المعتاد، أغلق التحويلة إذا كانت المسافة صحيحة أو تحقق منها مرة أخرى.", "VehicleListing_NRM_KMInCheck": "تحقق من الكيلومترات", "VehicleListing_NRM_CloseNRM": "إغلاق التحويلة", "VehicleListing_OutofService_Reason_Accident": "حادث / في الورشة", "VehilceListing_OutofService_Period": "الفترة في خارج الخدمة", "TrafficFines": {"trafficFines": "المخالفات المرورية", "home": "الرئيسية", "createFine": "إنشاء غرامة", "noFines": "لا توجد مخالفات", "searchPlaceholder": "ابحث برقم الحجز، أو رقم اللوحة، أو رقم التذكرة، أو رقم الاتفاقية", "columns": {"violationNo": "رقم المخالفة", "plateNo": "رقم اللوحة", "fineDate": "تاريخ المخالفة", "chargeStatus": "حالة التسديد", "paymentStatus": "حالة الدفع", "bookingNo": "رق<PERSON> الحجز", "agreementNo": "رقم الاتفاقية", "issuedBranch": "فرع إصدار المخافة", "amount": "المبلغ"}, "statuses": {"DRIVER": "السائق", "DEBTOR": "المدين", "UNPAID": "غير مدفوع", "PAID": "مدفوع"}, "filters": {"bookingNo": "رق<PERSON> الحجز", "plateNo": "رقم اللوحة", "ticketNo": "رقم المخالفة", "agreementNo": "رقم الاتفاقية", "location": "الموقع", "chargeStatus": "حالة التسديد", "paymentStatus": "حالة الدفع", "dateRange": "نطاق التاريخ"}, "actions": {"actions": "الإجراءات", "printInvoice": "طباعة الفاتورة", "createPaymentLink": "إنشاء رابط دفع", "payFromDeposit": "الدفع من الإيداع", "viewPaymentLink": "عرض رابط الدفع"}, "securityDeposit": {"toastTitle": "الإيداع لا يغطي المخالفة", "toastDescription": "مبلغ الإيداع لا يغطي مبلغ المخالفة.", "withdrawFromDeposit": "سحب من الإيداع", "amountInDeposit": "المبلغ في الإيداع", "fineAmount": "مب<PERSON>غ المخالفة", "approvalCode": "رقم الموافقة", "pos": "ج<PERSON>از الدفع", "select": "اختر", "cardLast4Digits": "أخر 4 أرقام من البطاقة", "last4Digits": "أخر 4 أرقام", "customerConsent": "لقد حصلت على موافقة العميل", "cancel": "إلغاء", "approvalCodeRequired": "رقم الموافقة مطلوب", "cardLast4DigitsRequired": "أخر 4 أرقام من البطاقة مطلوبة", "posMachineRequired": "ج<PERSON><PERSON>ز الدفع مطلوب"}, "invoice": {"notFound": "الفاتورة غير موجودة", "checkInvoiceNo": "يرجى التحقق من رقم الفاتورة وإعادة المحاولة مرة أخرى", "downloadFailed": "فشل في التنزيل", "failedToDownloadInvoice": "فشل في تنزيل الفاتورة", "printFailed": "فشل في الطباعة", "failedToOpenPrintWindow": "فشل في فتح نافذة الطباعة"}, "paymentLink": {"paymentRequested": "تم طلب دفع فاتورة المرور", "customerWillReceiveLink": "سيتم إرسال رابط QuickPay للعميل لإكمال العملية", "quickPayLink": "رابط QuickPay", "paymentTotal": "إجمالي الدفع", "sar": "ريال", "backToInvoices": "العودة إلى الفواتير"}, "createTrafficFine": {"recordFine": "تسجيل غرامة مرور", "recordFineDescription": "تسجيل غرامة مرور جديدة", "trafficTicketDetails": "تفاصيل تذكرة المرور", "ticketNo": "رقم التذكرة", "enterTicketNo": "أدخل رقم التذكرة", "licenseNo": "رقم اللوحة", "enterLicenseNo": "أدخل رقم لوحة المركبة", "violationDate": "تاريخ المخالفة", "time": "الوقت", "violationDateAndTime": "تاريخ ووقت المخالفة", "amount": "المبلغ (ريال)", "enterAmount": "أ<PERSON><PERSON><PERSON> المبلغ", "cancel": "إلغاء", "getAgreementDetails": "الحصول على تفاصيل الاتفاقية", "agreementDetails": "تفاصيل الاتفاقية", "chargeStatus": "حالة الرسوم", "invoiceToDriver": "فاتورة للسائق", "municipality": "البلدية", "selectMunicipality": "اختر البلدية", "violationCode": "ر<PERSON>ز المخالفة", "selectViolationCode": "اختر رمز المخالفة", "agreementNo": "رقم الاتفاقية", "status": "الحالة", "driver": "السائق", "mobileNo": "رقم الجوال", "address": "العنوان", "checkInBranch": "فرع الدخول", "createTrafficInvoice": "إنشاء فاتورة مرور", "loadingDetails": "تفاصيل جارٍ تحميلها...", "dontCloseOrRefresh": "لا تغلق أو refresh هذه الشاشة", "loading": "جارٍ التحميل...", "errors": {"failedToFetchAgreementDetails": "فشل في الحصول على تفاصيل الاتفاقية", "ticketNumberRequired": "رقم التذكرة مطلوب", "fineCreatedForThisTicket": "تم إنشاء غرامة بالفعل لهذه التذكرة", "plateNoRequired": "رقم اللوحة مطلوب", "violationDateRequired": "تاريخ المخالفة مطلوب", "timeRequired": "الوقت مطلوب", "amountRequired": "المبلغ مطلوب", "municipalityRequired": "البلدية مطلوبة", "violationCodeRequired": "رمز المخالفة مطلوب"}, "dropoffDateTime": "تاريخ ووقت التسليم", "Traffic Invoice created successfully": "تم إنشاء فاتورة المرور بنجاح", "A copy of the invoice will be sent to the customer’s email address": "سيتم إرسال نسخة من الفاتورة إلى عنوان البريد الإلكتروني للعميل"}}, "AllBookings": {"title": "جميع الحجوزات", "all-bookings": "جميع الحجوزات", "refund-requests": "طلبات الاسترداد", "emptyMessage": "لا توجد حجوزات", "search": "البحث", "columns": {"bookingNo": "رق<PERSON> الحجز", "agreementNo": "رقم الاتفاقية", "pickupDateTime": "تاريخ الاستلام", "dropOffDateTime": "تاريخ التسليم", "pickupBranch": "فرع الاستلام", "dropOffBranch": "فرع التسليم", "status": "الحالة", "totalPrice": "السعر الكلي", "pickupDate": "تاريخ الاستلام", "dropOffDate": "تاريخ التسليم", "driver": "السائق", "vehicle": "المركبة", "total": "الإجمالي"}, "all-agreements": "كل الاتفاقيات"}, "Refund": {"requests": "طلبات الاسترداد", "columns": {"bookingNo": "رق<PERSON> الحجز", "amount": "المبلغ", "agreementNo": "رقم الاتفاقية", "pickupBranch": "فرع الاستلام", "dropOffBranch": "فرع التسليم", "pickupDateTime": "تاريخ الاستلام", "dropOffDateTime": "تاريخ التسليم", "refundAmount": "مست<PERSON><PERSON> (ريال)", "iban": "الايبان"}, "emptyMessage": "لا توجد طلبات استرداد", "search": "البحث", "recordRefund": "تسجيل الاسترداد", "resendIBANLink": "إعادة إرسال رابط الايبان", "resending": "جاري الإرسال...", "copyLink": "نسخ الرابط", "copied": "تم النسخ", "errors": {"failedToFetchRefundRequests": "فشل في استلام طلبات الاسترجاع"}, "toBeRefunded": "المبلغ المطلوب استرداده", "refunded": "تم الاسترداد", "copyIbanLink": "نسخ رابط الايبان", "issueRefund": "إصدار الاسترداد", "amountToRefund": "المبلغ المراد استرداده", "refundMethod": "طريقة الاسترداد", "cash": "نقداً", "bankTransfer": "تحويل بنكي", "ibanLinkSentToCustomer": "سيتم إرسال رابط IBAN إلى العميل", "customerWillReceiveLink": "سيتلقى العميل رابطًا على رقم الجوال المسجل لإدخال تفاصيل IBANلإتمام عملية الاسترداد.", "cancel": "إلغاء", "processRefund": "تسجيل الاسترداد", "requestRefund": "طلب الاسترداد", "success": "تم الاسترداد", "error": "خطأ", "refundRequestCreated": "تم إنشاء طلب الاسترداد بنجاح", "refundProcessed": "تم الاسترداد بنجاح"}, "Filters": {"pickupBranch": "فرع الاستلام", "dropOffBranch": "فرع التسليم", "dropOffTime": "وقت التسليم", "pickupTime": "وقت الاستلام", "status": "الحالة", "ibanStatus": "حالة الايبان", "values": {"filled": "مملوء", "notFilled": "غير مملوء", "upcoming": "قادم", "no-show": "ل<PERSON> يحضر", "ongoing": "ح<PERSON><PERSON> جاري", "late-return": "تأخير في التسليم", "cancelled": "ملغي", "suspended": "معلق", "completed": "مكتمل", "timeFilter": {"all": "كل الوقت"}}, "Search": "البحث"}, "CashRegister": {"title": "السجل المالي", "closeRegister": "إغلاق السجل", "confirmDepositAndCloseRegister": "تأكيد الإيداع وإغلاق السجل", "closeCashRegister": "إغلاق السجل المالي", "closeCashRegisterDescription": "هل أنت متأكد أنك تريد إغلاق السجل المالي؟", "confirmDeposit": "تأكيد الإيداع", "pleaseSelectABankFirst": "ير<PERSON>ى اختيار البنك أولا", "nameOfBank": "اسم البنك", "selectABank": "اختر بنك", "statuses": {"OPEN": "مفتوح", "PRE_CLOSED": "في انتظار تأكيد الإيداع", "CLOSED": "مغلق"}, "openTime": "وقت الفتح", "closeTime": "وقت الإغلاق", "closedBy": "مغلق بواسطة", "approvedBy": "معتمد بواسطة", "columns": {"item": "النوع", "cash": "نقد", "pos": "آلة الدفع", "bankTransfer": "تحويل بنكي", "total": "المجموع"}, "rows": {"openingBalance": "الرصيد الافتتاحي", "received": "المبلغ المستلم", "withdraw": "المبلغ المسحوب", "bankDeposit": "المبلغ المودع", "closingBalance": "الرصيد النهائي"}, "actions": {"moveFullBalanceToNextDayAndCloseRegister": "نقل الرصيد كاملا إلى اليوم القادم وإغلاق السجل", "createDepositRecord": "تسجيل إيداع مبلغ", "confirmDepositAndCloseRegister": "تأكيد الإيداع وإغلاق السجل", "cancel": "إلغاء", "confirm": "تأكيد"}, "cashBalancePresent": "لديك رصيد نقدي", "cashBalancePresentDescription": "لديك رصيد نقدي بقيمة SAR {totalBalance}", "amountToDeposit": "المبلغ المراد إيداعه", "remainingBalance": "الرصيد المتبقي (ينقل إلى الرصيد المتبقي لليوم القادم)", "toast": {"deposit": {"success": {"title": "تم النجاح", "description": "تم تسجيل إيداع المبلغ بنجاح للسجل #{displayRegisterNo}"}, "error": {"title": "خطأ في تسجيل الإيداع، يرجى المحاولة مرة أخرى", "description": "خطأ في تسجيل الإيداع للسجل #${displayRegisterNo}"}, "confirm": {"title": "تم إغلاق السجل بنجاح", "description": "تم تأكيد الإيداع والسجل #{displayRegisterNo} أغلق بنجاح"}}, "close": {"success": {"title": "تمت العملية بنجاح", "description": "تم إغلاق السجل #${displayRegisterNo} بنجاح"}, "error": {"title": "خطأ في إغلاق السجل، يرجى المحاولة مرة أخرى", "description": "خطأ في إغلاق السجل #${displayRegisterNo}"}}, "failedToCloseRegister": "فشل في إغلاق السجل"}, "noCashRegisterDataAvailable": "لا توجد بيانات للسجل المالي"}, "invoice": {"title": "الفاتورة", "invoiceNo": "رقم الفاتورة", "date": "التاريخ", "amount": "المبلغ", "status": "الحالة", "paymentMethod": "طريقة الدفع", "paymentStatus": "حالة الدفع", "actions": {"viewDetails": "عرض التفاصيل", "downloadInvoice": "تنزيل الفاتورة"}, "statuses": {"PAID": "مدفوع", "UNPAID": "غير مدفوع"}, "results": {"success-title": "تم إغلاق الاتفاقية بنجاح", "success-description": "تم إرسال نسخة من اتفاقية الإيجار إلى البريد الإلكتروني للعميل", "pending-title": "في انتظار إغلاق تأجير", "pending-description": "جاري معالجة الفاتورة. يرجى الانتظار...", "error-description": "فشل إنشاء الفاتورة. يرجى المحاولة مرة أخرى.", "error-paragraph": "سيقوم النظام تلقائيًا بإعادة المحاولة مع تأجير ونقل الحجز إلى صفحة 'يحتاج إلى إجراء'. سيتم تحديث حالة المركبة بمجرد إغلاق تأجير.", "authorization": "التفويض", "toast": {"retry": {"success": {"title": "نجحت إعادة المحاولة", "description": "تمت إعادة محاولة التفويض بنجاح."}, "failed": {"title": "فشلت إعادة المحاولة", "description": "حد<PERSON> خطأ أثناء إعادة المحاولة."}, "error": {"title": "خطأ", "description": "حد<PERSON> خطأ أثناء إعادة محاولة التفويض."}}, "status": {"success": {"title": "نجاح", "description": "تم إنشاء الفاتورة بنجاح."}, "failed": {"title": "فشل عند إنشاء الفاتورة", "description": "فشل إنشاء الفاتورة. يرجى المحاولة مرة أخرى."}, "error": {"title": "فشل عند إنشاء الفاتورة", "description": "حد<PERSON> خطأ عند إنشاء الفاتورة."}, "exception": {"title": "خطأ", "description": "حد<PERSON> خطأ أثناء استطلاع حالة الفاتورة."}}, "resend": {"success": {"title": "تم إعادة إرسال الفاتورة بنجاح", "description": "تم إعادة إرسال الفاتورة إلى هيئة الزكاة والضريبة والجمارك."}, "failed": {"title": "فشل", "description": "فشل إعادة إرسال الفاتورة إلى هيئة الزكاة والضريبة والجمارك، يرجى المحاولة مرة أخرى."}, "error": {"title": "خطأ", "description": "حدث خطأ خلال الإرسال إلى هيئة الزكاة والضريبة والجمارك."}}, "view": {"failed": {"title": "فشل", "description": "فشل عرض الفاتورة، يرجى المحاولة مرة أخرى."}, "error": {"title": "خطأ", "description": "حدث خطأ خلال الإرسال إلى هيئة الزكاة والضريبة والجمارك."}}, "credit_note": {"success": {"title": "تم إنشاء إشعار الائتمان بنجاح", "description": "تم إنشاء إشعار الائتمان بنجاح."}, "failed": {"title": "فشل خلال إنشاء إشعار الائتمان", "description": "حد<PERSON> خطأ خلال إنشاء إشعار الائتمان."}, "error": {"title": "خطأ", "description": "حد<PERSON> خطأ خلال إنشاء إشعار الائتمان."}}, "debit_note": {"success": {"title": "تم إنشاء إشعار الخصم بنجاح", "description": "تم إنشاء إشعار الخصم بنجاح."}, "failed": {"title": "فشلت عملية إنشاء إشعار الخصم", "description": "حد<PERSON> خطأ خلال إنشاء إشعار الخصم."}, "error": {"title": "خطأ", "description": "حد<PERSON> خطأ خلال إنشاء إشعار الخصم."}}}, "retry": "إعادة المحاولة", "try-again": "حاول مرة أخرى", "retrying": "جاري إعادة المحاولة...", "reason": "السبب", "invoice-status-pending": "جاري إنشاء الفاتورة", "invoice-status-error": "إعادة إنشاء الفاتورة", "invoice-status-success": "طباعة الفاتورة", "cta-success": "العودة إلى الحجوزات"}, "list": {"columns": {"invoiceNumber": "رقم الفاتورة", "issueDate": "تاريخ الفاتورة", "invoiceConfigType": "نوع الفاتورة", "payStatus": "الحالة", "bookingNumber": "رق<PERSON> الحجز", "agreementNumber": "رقم الاتفاقية", "branchName": "فرع إصدار الفاتورة", "totalInvoiceAfterVat": "المبلغ (ريال)", "totalAmountPaid": "الر<PERSON>يد الإجمالي", "invoiceStatus": "حالة هيئة الزكاة والضريبة والجمارك"}, "emptyMessage": "لا توجد فواتير.", "searchPlaceholder": "البحث برقم الفاتورة، رقم الاتفاقية، رقم الحجز", "filters": {"paymentStatus": {"title": "حالة الدفع", "paid": "مدفوعة", "unpaid": "غير مدفوعة"}, "invoiceStatuses": {"title": "حالة هيئة الزكاة والضريبة والجمارك", "pending": "قيد الانتظار", "success": "ناجحة", "error": "خطأ", "no_zatca": "لا يوجد زاتكا", "cancelled": "إلغاء"}, "invoiceConfigTypes": {"title": "نوع الفاتورة", "driver_invoice": "السائق", "combination": "المدين", "pre_billing": "الفوترة المسبقة", "credit": "إشعار دائن", "debit": "إشعار مدين", "traffic_fine": "غرامة مرورية", "damage": "ضرر", "cancellation": "مذكرة الائتمان إلغاء"}, "search": {"bookingNumbers": "رق<PERSON> الحجز", "agreementNumbers": "رقم الاتفاقية", "invoiceNumbers": "رقم الفاتورة"}, "branch": {"title": "فرع"}}, "stats": {"totalAmount": "المبلغ الإجمالي", "totalAmountDue": "إجمالي الرصيد المستحق"}, "actions": {"cancel": "إلغاء الفاتورة", "resend": "إعادة إرسال إلى هيئة الزكاة والضريبة والجمارك", "print": "طباعة الفاتورة", "create": "إنشاء فاتورة", "createDebitNote": "مذكرة الخصم", "createCreditNote": "مذكرة دائن"}, "title": "فواتير", "toast": {"noteCreation": {"creditNote_creation_success": {"title": "نجاح", "description": "تم إنشاء إشعار الدائن بنجاح."}, "debitNote_creation_success": {"title": "نجاح", "description": "تم إنشاء إشعار الدائن بنجاح."}, "creation_error": {"title": "خطأ", "description": "فشل إنشاء إشعار دائن، يرجى المحاولة مرة أخرى."}}}}, "creditDebitNote": {"issueDate": "تاريخ إصدار الفاتورة", "originalInvoiceNumber": {"label": "رقم الفاتورة الأصلي", "placeholder": "أدخل رقم الفاتورة الأصلي"}, "agreementNumber": {"label": "رقم الاتفاقية", "placeholder": "سيظهر رقم الاتفاقية هنا"}, "reason": {"label": "السبب", "placeholder": "اختر السبب"}, "remarks": {"label": "ملاحظات (اختياري)", "placeholder": "ادخل ملاحظاتك هنا"}, "financial_details": "التفاصيل المالية", "credit_note_details": "تفاصيل إشعار الائتمان", "debit_note_details": "تفاصيل مذكرة الخصم", "credit_note": "مذكرة دائن", "debit_note": "مذكرة الخصم", "creditedAmountBeforeVAT": {"label": "المبلغ المعتمد قبل ضريبة القيمة المضافة", "placeholder": "أ<PERSON><PERSON><PERSON> المبلغ"}, "vatPercentage": "نسبة ضريبة القيمة المضافة %", "vatAmount": "مبلغ ضريبة القيمة المضافة", "totalCredit": "إجمالي الائتمان", "success_credit_cta": "إنشاء مذكرة دائن", "success_debit_cta": "إنشاء مذكرة خصم", "cancel_cta": "إلغاء", "cancel_credit_description": "سيؤدي هذا إلى إلغاء الفاتورة وإنشاء إشعار دائن.", "cancel_debit_description": "سيؤدي هذا إلى إلغاء الفاتورة وإنشاء إشعار مدين.", "new_credit_note": "فتح مذكرة دائن جديد", "new_debit_note": "فتح مذكرة خصم جديد"}, "card": {"title": "عرض الفواتير", "caption": "فواتير الحجز", "description": "ستظهر جميع الفواتير ضمن هذا الحجز هنا", "simplified": "فاتورة ضريبية مبسطة", "noInvoice": "لم يتم العثور على فواتير لهذا الحجز.", "cta": {"viewMore": "عر<PERSON> المزيد", "close": "إغلاق"}, "toast": {"failed": {"title": "فشل جلب الفواتير", "description": "تعذر جلب الفواتير للحجز."}, "error": {"title": "خطأ", "description": "حد<PERSON> خطأ أثناء جلب الفواتير."}}}}, "NRM": {"title": "مركبات الفرع", "searchPlaceholder": "ابحث برقم اللوحة", "emptyMessage": "لا توجد مركبات", "newNRM": "تحويلة جديدة", "loadingVehicleDetails": "جاري تحميل التفاصيل...", "noVehicleDetailsAvailable": "لا توجد تفاصيل للمركبة", "tabs": {"readyForRental": "جاهزة للتأجير", "rented": "مستأجرة", "needsPrep": "يحتاج تجهيز", "nrms": "التحويلات", "outOfService": "<PERSON>ارج الخدمة"}, "filters": {"location": "الموقع", "model": "الموديل", "group": "المجموعة", "reason": "السبب", "from": "من", "to": "إ<PERSON><PERSON>"}, "columns": {"plateNo": "رقم اللوحة", "group": "المجموعة", "location": "الموقع", "vehicle": "المركبة", "status": "الحالة", "fuel": "الوقود", "km": "الكيلومترات", "actions": "الإجراءات", "bookingNo": "رق<PERSON> الحجز", "checkInBranch": "فرع الوصول", "checkInDate": "تاريخ الوصول", "reason": "السبب", "waitingPeriod": "فترة الانتظار", "nrmNo": "رقم الحركة الداخلية", "from": "من", "to": "إ<PERSON><PERSON>", "model": "الموديل", "driver": "السائق", "nrmStartTime": "وقت بدء التحويلة", "nrmCloseTime": "وقت إغلاق التحويلة"}, "actions": {"openNRM": "بدء التحويلة", "moveToService": "نقل إلى خارج الخدمة", "cancel": "إلغاء", "processing": "جاري المعالجة...", "moveToAvailable": "نقل إلى جاهزة للتأجير", "moveVehicle": "نقل المركبة", "closeNRM": "إغلاق التحويلة"}, "mileage": "الكيلومترات", "fuelLevel": "مستوى الوقود", "checkoutDetails": "التفاصيل عند الخروج", "driverName": "اسم السائق (اختياري)", "searchByDriverName": "ابحث باسم السائق", "reason": "السبب", "selectReason": "اختر السبب", "moveTo": "نقل إلى", "selectLocation": "اختر الموقع", "checkoutTime": "وقت الخروج", "remarks": "الملاحظات (اختياري)", "remarksPlaceholder": "ادخل الملاحظات إذا كان لديك", "loadingBranches": "جاري تحميل الفروع...", "loadingReasons": "جاري تحميل الأسباب...", "alertTitle": "سيتم نقل المركبة إلى خارج الخدمة", "alertDescription": "سيتم نقل المركبة تلقائيًا إلى \"خارج الخدمة\" لوجودها في ورشة العمل.", "OutOfServiceDialog": {"title": "نقل إلى خارج الخدمة", "description": "اختر السبب لنقل المركبة إلى خارج الخدمة", "reason": "السبب لنقل المركبة إلى خارج الخدمة", "chooseReason": "اختر السبب لنقل المركبة إلى خارج الخدمة"}, "fuelLevels": {"1": "ربع", "2": "نصف", "3": "ثلث", "4": "كامل"}, "available": {"title": "نقل إلى جاهزة للتأجير", "description": "نقل المركبة إلى متاح سيجعلها متاحة لتعيينها إلى السائقين، هل أنت متأكد أنك تريد نقل المركبة؟", "toast": {"success": {"title": "تم النجاح", "description": "تم نقل المركبة إلى متاح بنجاح"}, "error": {"title": "خطأ", "description": "فشل نقل المركبة إلى متاح"}}}, "closeDialog": {"title": "إغلاق التحويلة #{nrmId}", "checkoutDetails": "تفاصيل الخروج", "driverName": "اسم السائق", "searchByName": "البحث بالاسم", "reason": "السبب*", "moveTo": "النقل إلى*", "checkoutTime": "وقت الخروج", "checkinDetails": "تفاصيل الوصول", "kmIn": "الكيلومترات عند الوصول", "kmInPlaceholder": "مثال: 123,800", "fuelIn": "مستوى الوقود عند الوصول", "loadingFuelLevels": "جاري تحميل مستويات الوقود...", "selectFuelLevel": "اختر مستوى الوقود", "highMileageWarning": "يبدو أن الكيلومترات عند الوصول أعلى من المعتاد. يرجى التأكيد.", "lowMileageError": "لا يمكن أن تكون الكيلومترات عند الوصول أقل من القراءة الحالية ({reading})", "remarks": "ملاحظات (اختياري)", "remarksPlaceholder": "أدخل ملاحظة إذا كان لديك", "cancel": "إلغاء", "closeNrm": "إغلاق التحويلة", "success": {"title": "تم إغلاق التحويلة بنجاح"}, "error": {"title": "خطأ في إغلاق التحويلة"}}}, "timestamp": {"Today": "اليوم", "Tomorrow": "غداً", "NOW": "الآن", "1 day passed": "مضى يوم واحد", "in {minutes} minutes": "بعد {minutes} دقيقة", "in 1 hour": "بعد ساعة", "in {hours} hours": "بعد {hours} ساعة", "{days} days passed": "{days} أيام مرت"}, "waitingTime": {"days": "أيام", "hours": "ساعات", "minutes": "دقائق", "weeks": "أسابيع", "day": "يوم", "hour": "ساعة", "minute": "دقيقة", "week": "أسبوع"}, "sidebar": {"branch": "الفرع", "myBookings": "حجوزات الفرع", "vehicles": "مركبات الفرع", "branchSettings": "إعدادات الفرع", "financials": "المالية", "cashRegister": "الصندوق المالي", "payments": "المدفوعات", "refunds": "الاستردادات المالية", "invoices": "الفواتير", "rewards": "المكافآت", "quickPay": "الدفع سريع", "categories": "الفئات", "make": "الشركة المصنعة", "model": "الموديل", "vehicleSpecs": "مواصفات المركبة", "customers": "العملاء", "drivers": "السائقون", "partners": "الشركاء", "allReservations": "جميع الحجوزات", "reservations": "الحجوزات", "location": "الموقع", "cities": "المدن", "branchesYards": "الفروع والساحات", "tariff": "التعرفة", "b2c": "أفر<PERSON> (B2C)", "b2b": "شركات (B2B)", "promotions": "العروض الترويجية", "allBookings": "جميع الحجوزات", "adminFinancials": "مالية الإدارة", "allPayments": "جميع المدفوعات", "allRefunds": "جميع المبالغ المستردة", "addons": "الإضافات", "allBookingsAndAgreements": "جميع الحجوزات والاتفاقيات", "allVehicles": "جميع المركبات"}, "branches": {"columns": {"name": "الاسم", "code": "الكود", "type": "النوع", "city": "المدينة", "region": "المنطقة", "longitude": "الطول", "latitude": "العرض", "location": "الموقع"}}, "customer": {"columns": {"customerID": "رقم تعريف العميل", "customerName": "اسم العميل", "mobileNumber": "رقم الجوال", "email": "الب<PERSON>يد الإلكتروني", "driverCode": "السائق", "emailVerified": "البريد الإلكتروني موثّق", "mobileVerified": "رقم الجوال موثّق", "profileCompleted": "اكتمل الملف الشخصي"}, "pageTitle": "العملاء", "searchPlaceholder": "ابحث حسب اسم العميل أو الجوال أو البريد الإلكتروني", "emptyMessage": "لا توجد معلومات عن العميل.", "customerProfile": {"pageTitle": "العميل", "label": {"title": "المسمى", "firstName": "الاسم الأول", "lastName": "اسم العائلة", "mobileNumber": "رقم الجوال", "email": "الب<PERSON>يد الإلكتروني", "dateOfBirth": "تاريخ الميلاد", "profileCompleted": "اكتمل الملف الشخصي", "createdOn": "تاريخ الإنشاء", "updatedOn": "تم التحديث في", "driverCode": "رمز السائق", "customer": "العميل", "language": "اللغة"}, "heading": {"basicInfo": "معلومات أساسية", "driverInfo": "معلومات السائق"}}, "navs": {"customerProfile": "م<PERSON><PERSON> العميل", "reservations": "الحجوزات", "notifications": "الإشعارات", "agreements": "الاتفاقيات"}, "reservations": {"columns": {"referenceNo": "الرقم المرجعي", "providerReferenceNo": "رق<PERSON> حجز <PERSON>", "rentalSum": "مب<PERSON>غ الإيجار", "totalPrice": "السعر الإجمالي", "status": "حالة الحجز", "paymentType": "نوع الدفع", "bookingDateTime": "تاريخ الحجز", "pickupDateTime": "وقت الاستلام", "dropOffDateTime": "وقت التسليم"}, "searchPlaceholder": "ابحث بواسطة الرقم المرجعي أو رقم حجز carpro", "emptyMessage": "لا توجد حجوزات للعملاء."}, "notifications": {"columns": {"id": "معرّف", "recipient": "المستلم", "status": "الحالة", "type": "النوع", "createdOn": "تم الإنشاء في", "message": "رسالة", "response": "الاستجابة"}, "searchPlaceholder": "ابح<PERSON> حسب المعرف أو المستلم", "emptyMessage": "لا توجد إشعارات للعميل"}, "agreements": {"columns": {"agreementNo": "رقم الاتفاقية", "reservationNo": "رق<PERSON> الحجز", "agreementStatus": "حالة الاتفاقية", "licenseNo": "رقم الرخصة", "totalAmount": "المبلغ الإجمالي", "totalPaid": "إجمالي المدفوع", "totalBalance": "إجمالي الرصيد"}, "searchPlaceholder": "البحث حسب المعرف أو المستلم", "emptyMessage": "لا توجد أي اتفاقيات"}}, "createAgreement": {"pageTitle": "إنشاء اتفاقية", "bookingDetails": {"pageTitle": "تفاصيل الحجز", "bookingOn": "تم الحجز في", "pickup": "الاستلام", "today": "اليوم", "now": "الآن", "orignalPickupTime": "وقت الاستلام الأصلي", "dropoff": "التسليم", "discount": "الخصم", "preferences": "تفضيلات العميل", "preferencesDesc": "يمكنك تعديلها في الخطوات التالية", "group": "المجموعة", "paymentsRefunds": "المدفوعات والاستردادات", "noPayments": "لا توجد معاملات!", "loayltyProgramTitle": "برنامج الولاء", "loayltyProgram": "برنامج الولاء", "accountNumber": "رقم الحساب", "points": "النقاط / الأميال"}, "assignVehicle": {"pageTitle": "تعيين مركبة", "selectedVehicle": "المركبة المحددة", "suggestedVehicles": "المركبات المقترحة", "allBranchesVehicles": "مركبات جميع الفروع", "allReadyVehicles": "جميع المركبات الجاهزة", "viewallReadyVehicles": "عرض جميع المركبات الجاهزة", "cta": {"select": "اختيار", "upgrade": "خيارات الترقية", "downgrade": "تخفيض", "cancel": "إلغاء", "replace": "استبدال المركبة", "confirmDowngrade": "تأكيد التخفيض", "resetFilters": "إعادة تعيين الفلاتر", "viewAll": "عرض جميع مركبات الفرع", "change": "تغيير المركبة"}, "noResults": "لم يتم العثور على نتائج", "noResultsDesc": "حاول تعديل أو إعادة تعيين الفلاتر", "replaceModal": {"title": "هل تريد استبدال المركبة المحددة؟", "description": "سيتم إزالة المركبة المحددة واستبدالها بالجديدة"}, "downgradeModal": {"title": "تأكيد تخفيض المركبة", "description": "قد يتطلب التغيير إلى مركبة منخفضة المستوى رد مبلغ للعميل"}, "upgradeModel": {"title": "الترقية إلى", "description": "اختر نوع الترقية", "reasonForUpgrade": "سبب الترقية", "approvalText": "لقد حصلت على موافقة مشرف / مدير الفرع", "paidUpgrade": "ترقية مدفوعة", "freeupgrade": "ترقية مجانية", "successCta": "ترقية", "negativeCta": "إلغاء"}, "vehicleChange": {"title": "إشعار تغيير المركبة", "description": "لا تنطبق العناصر التالية على المركبة المختارة", "cta": {"submit": "تأكيد"}}}, "payment": {"discount": {"title": "الخصومات", "dialogTitle": "خصومات الشركات", "dialogDesc": "سيتم فقدان الخصم الحالي إذا قمت بتطبيق خصم جديد", "cta": {"corporateDiscount": "خصم شركة", "availableDiscount": "الخصومات المتاحة", "issueRefund": "إصدار استرداد", "collectPayment": "تحصيل الدفع"}, "editProfile": "تعديل الملف الشخصي", "editProfileDesc": "يمكنك تعديل ملفك الشخصي هنا. اضغط على حفظ عند الانتهاء.", "payment": "الدفع", "noPayments": "لا توجد مدفوعات!"}, "refund": {"title": "إصدار استرداد", "amountToRefund": "المبلغ المطلوب استرداده", "refundMethod": "طريقة الاسترداد", "cash": "نقداً", "bankTransfer": "تحويل بنكي", "IbanAlertTitle": "سيتم إرسال رابط IBAN إلى العميل", "IbanAlertDesc": "سيستلم العميل رابطًا على رقم الجوال المسجل لإدخال بيانات IBAN لاستكمال عملية الاسترداد", "cta": {"negative": "إلغاء", "processRefund": "تنفيذ الاسترداد", "requestRefund": "طلب استرداد"}}}, "rentalAgreement": "إنشاء اتفاقية الإيجار", "Create rental agreement": "إنشاء اتفاقية الإيجار"}, "bookingDetail": {"cta": {"extend": "<PERSON><PERSON><PERSON><PERSON><PERSON> الحجز", "close": "إغلاق الاتفاقية", "start": "بدء الاتفاقية", "edit": "تعديل الحجز"}, "cancelBy": "تم إلغاء الحجز بواسطة", "assignedVehicle": "المركبة المخصصة", "inspectionReport": "تقرير الفحص", "discount": "الخصم", "reason": "السبب", "Booking": " الح<PERSON><PERSON>", "on": "في", "amount": "المبلغ", "paymentLinks": "روابط الدفع", "currency": "ريال", "createdOn": "تاريخ الإنشاء", "trafficFine": "مخالفة مرورية", "agreementExtension": "تمديد الاتفاقية", "type": "النوع", "Paid": "مدفوع", "link": "رابط", "status": "الحالة", "active": "نشط", "expired": "منتهي"}, "fleetManagement": {"title": "إدارة الأسطول", "description": "إدارة أسطول المركبات الخاص بك", "vehicles": {"title": "المركبات", "available": "المركبات المتاحة", "inService": "في الخدمة", "outOfService": "<PERSON>ارج الخدمة", "maintenance": "الصيانة", "cleaning": "التنظيف", "fueling": "التزود بالوقود"}, "reports": {"title": "التقارير", "utilization": "تقرير الاستخدام", "maintenance": "تقرير الصيانة", "fuel": "تقرير الوقود", "damage": "تقرير الأضرار"}, "maintenance": {"title": "الصيانة", "scheduled": "الصيانة المجدولة", "history": "تاريخ الصيانة", "upcoming": "الصيانة القادمة"}, "inventory": {"title": "المخزون", "totalVehicles": "إجمالي المركبات", "activeVehicles": "المركبات النشطة", "inactiveVehicles": "المركبات غير النشطة"}, "all-vehicles": {"searchPlaceholder": "بحث برقم اللوحة", "emptyMessage": "لا يوجد سيارات", "column": {"plateNo": "رقم اللوحة", "group": "المجموعة", "vehicle": "السيارة", "category": "الفئة", "statusAndSubStatus": "الحالة والحالة الفرعية", "location": "الموقع", "serviceType": "الخدمة"}, "filters": {"serviceType": "الخدمة", "location": "الموقع", "group": "المجموعة", "status": "الحالة", "subStatus": "الحالة الفرعية", "category": "الفئة", "vehicle": "السيارة", "model": "الموديل"}, "tabs": {"overview": "الملخص", "details": "التفاصيل", "condition": "الحالة", "history": "التاريخ", "documents": "الوثائق"}}}, "cancelBooking": {"title": "إلغاء الحجز", "description": "سيتم وضع علامة ملغي على الحجز", "reason_field": "سبب الإلغاء", "reason_field_placeholder": "<PERSON><PERSON><PERSON><PERSON> السبب", "reasons": {"reason_1": "لا توجد مركبات متاحة", "reason_2": "عميل مشبوه", "reason_3": "مرفوض من تأجير/تم", "reason_4": "لم يُظهر بطاقة ائتمان", "reason_5": "لم يتمكن العميل من الدفع", "reason_6": "أ<PERSON><PERSON><PERSON>"}, "toast": {"success": {"title": "تم إلغاء الحجز بنجاح", "description": "تم إلغاء الحجز بنجاح"}, "failed": {"title": "فشل", "description": "فشل في إلغاء الحجز"}, "error": {"title": "خطأ", "description": "حد<PERSON> خطأ غير متوقع"}}, "cta": {"cancel": "إلغاء", "submit": "إلغاء الحجز", "viewMore": "عرض تفاصيل الحجز", "back": "العودة إلى الحجوزات"}, "modalTitle": "تم إلغاء الحجز", "modalDesc": "تم إلغاء الحجز بنجاح", "inventory": {"title": "المخزون", "totalVehicles": "إجمالي المركبات", "activeVehicles": "المركبات النشطة", "inactiveVehicles": "المركبات غير النشطة"}, "all-vehicles": {"searchPlaceholder": "بحث برقم اللوحة", "emptyMessage": "لا يوجد سيارات", "column": {"plateNo": "رقم اللوحة", "group": "المجموعة", "vehicle": "السيارة", "category": "الفئة", "statusAndSubStatus": "الحالة والحالة الفرعية", "location": "الموقع", "serviceType": "الخدمة"}, "filters": {"serviceType": "الخدمة", "location": "الموقع", "group": "المجموعة", "status": "الحالة", "subStatus": "الحالة الفرعية", "category": "الفئة", "vehicle": "السيارة", "model": "الموديل"}, "tabs": {"overview": "الملخص", "details": "التفاصيل", "condition": "الحالة", "history": "التاريخ", "documents": "الوثائق"}}, "dialog": {"title": "إلغاء الحجز", "description": "جارٍ إلغاء الحجز", "waitingMessage": "يرجى الانتظار قليلاً أثناء إنشاء الحجز وإعادة التوجيه إلى صفحة الدفع"}, "refund": {"title": "استرداد المبلغ بعد الإلغاء", "description": "إذا كان هناك أي مستحقات، يمكنك استردادها بعد الإلغاء. سيتم استرداد المدفوعات عبر الإنترنت تلقائيًا خلال 14 يومًا."}}, "discount": {"search_placehodler": "ابحث عن كود الخصم...", "no_search_results": "لم يتم العثور على نتائج.", "table": {"company": "الشركة", "code": "الكود", "amount": "المبلغ"}, "remove_discount_title": "إزالة الخصم", "remove_discount_desc": "سيتم إزالة الخصم من الحجز", "cta": {"submit": "إزالة", "negative": "إلغاء"}, "toast": {"select": {"success": {"title": "تمت إضافة الخصم بنجاح"}}, "remove": {"success": {"title": "تمت إزالة الخصم بنجاح"}}}, "discount_code": "<PERSON><PERSON><PERSON> الخصم", "percentage": "نسبة الخصم", "Discount code": "<PERSON><PERSON><PERSON> الخصم", "Percentage": "النسبة المئوية"}, "driverProfile": {"notAvailable": "<PERSON>ير متاح", "driverInformation": "معلومات السائق", "nationality": "الجنسية", "idNumber": "رقم الهوية", "idCode": "رمز الهوية", "dateOfBirth": "تاريخ الميلاد", "age": "العمر", "driverLicense": "رخصة القيادة", "origin": "الأصل", "licenseNumber": "رقم الرخصة", "expiryDate": "تاريخ الانتهاء", "lastBookings": "آخر 5 حجوزات", "upcoming": "قادمة", "noBookingInformation": "لا توجد معلومات حجز متاحة", "driverNotFound": "لم يتم العثور على السائق", "days": "{count, plural, one {يوم واحد} other {# أيام}}", "noShows": "{count, plural, =0 {لا يوجد تغيب} one {تغيب واحد} other {# حالات تغيب}}", "agreements": "{count, plural, =0 {لا توجد اتفاقيات} one {اتفاقية واحدة} other {# اتفاقيات}}"}, "refundRequest": {"Refund recorded": "تم تسجيل الاسترداد", "Refund has been recorded successfully": "تم تسجيل الاسترداد بنجاح", "View Booking": "عر<PERSON> الحجز", "Refund request": "طلب استرداد", "Record refund": "تسجيل الاسترداد", "Amount refunded": "المبلغ المسترد", "SAR": "ريال سعودي", "Account holder name": "اسم صاحب الحساب", "Enter account holder name": "أد<PERSON>ل اسم صاحب الحساب", "IBAN Number": "رق<PERSON> الآيبان", "Enter IBAN number": "أدخل رقم الآيبان", "Bank Name": "اسم البنك", "Enter bank name": "أد<PERSON>ل اسم البنك", "Remarks": "ملاحظات", "Enter bank transfer reference number": "أدخل رقم مرجع تحويل البنك", "IBAN letter": "<PERSON>ر<PERSON> ال<PERSON><PERSON><PERSON>", "Upload Document": "تحميل المستند", "Preview": "معاينة", "Cancel": "إلغاء", "Error": "خطأ في الاسترداد"}, "debtors": {"title": "الشركاء", "create": "إنشاء", "filters": {"service": "الخدمة", "status": "الحالة", "lease": "التأجير الطويل (Lease)", "rental": "التأجير اليومي", "ucs": "بيع السيارات", "commercial": "تجاري", "active": "نشط", "inactive": "غير نشط", "noRateCard": "لا توجد بطاقة تعرفة"}, "search": {"placeholder": "البحث باسم الشركة", "noDebtors": "لم يتم العثور على أي نتائج مطابقة لبحثك"}, "columns": {"debtor": "الشركاء", "debtorCode": "رقم الشريك", "debtorGroup": "مجموعة الشركاء", "service": "الخدمة", "status": "الحالة", "debtorManager": "الموظف المسؤول"}, "edit": "تعديل", "modal": {"debtorProfile": "إضافة شريك جديد", "debtorGroup": "مجموعة شركاء جديدة", "deactivateDebtor": "تعليق الشريك؟", "deactivateDebtorDesc": "هل أنت متأكد من رغبتك في تعطيل الشريك؟ سيتم أيضًا تعليق أي بطاقات تعرفة مرتبطة بالشريك", "debtorCreated": "تم إنشاء الشريك", "debtorCreatedDescription": "تم إنشاء حساب المدين الشريك! هل ترغب في إنشاء بطاقة تعرفة له؟", "btn": {"skip": "تخطي"}}, "btn": {"deactivate": "تعليق", "deactivating": "جاري التعليق...", "cancel": "إلغاء", "createDebtor": "إنشاء شريك", "updateDebtor": "تحديث الشريك"}, "createDebtor": {"title": "إنشاء شريك جديد", "desc": "إضافة تفاصيل الشريك الجديد"}, "debtorProfile": {"title": "التفاصيل", "label": {"debtorName": "اسم الشريك بالانجليزية", "debtorNameAr": "اسم الشريك بالعربية", "debtorGroup": "مجموعة الشركاء", "vatNumber": "رقم ضريبة القيمة المضافة", "debtorCode": "رقم الشريك", "sapCode": "رقم SAP", "companyType": "نوع الشريك", "crNo": "رقم السجل التجاري"}, "placeholder": {"debtorName": "اسم المدين", "debtorNameAr": "اسم الشريك بالعربية", "selectDebtorGroup": "اختر مجموعة الشركاء", "vatNumber": "رقم ضريبة القيمة المضافة", "debtorCode": "رقم الشريك", "sapCode": "رقم SAP", "companyType": "حدد نوع الشريك", "crNo": "رقم السجل التجاري"}, "option": {"clearSelectGroup": "مسح المجموعة المحددة"}}, "address": {"title": "عنوان", "label": {"shortAddress": "العنوان المختصر", "building": "رق<PERSON> المبنى", "street": "الشارع", "secondary": "الرقم الثانوي", "district": "المنطقة", "postalCode": "الر<PERSON>ز البريدي", "city": "المدينة"}, "placeholder": {"shortAddress": "العنوان المختصر", "building": "رق<PERSON> المبنى", "street": "الشارع", "secondary": "الرقم الثانوي", "district": "المنطقة", "postalCode": "الر<PERSON>ز البريدي", "city": "المدينة"}}, "contact": {"title": "معلومات الاتصال", "label": {"contactPersonName": "اسم جهة الاتصال", "emailAddress": "الب<PERSON>يد الإلكتروني", "phoneNumber": "رقم الهاتف"}, "placeholder": {"contactPersonName": "اسم جهة الاتصال", "emailAddress": "الب<PERSON>يد الإلكتروني", "phoneNumber": "رقم الهاتف"}}, "debtorManager": {"title": "الموظف المسؤول", "label": {"debtorManager": "الموظف المسؤول"}, "placeholder": {"debtorManager": "الموظف المسؤول"}}, "services": {"title": "الخدمات", "label": {"lease": "التأجير الطويل (Lease)", "rental": "التأجير اليومي", "ucs": "بيع السيارات", "commercial": "تجاري"}, "modal": {"title": "تعليق الشريك؟", "desc1": "هل أنت متأكد أنك تريد إزالة هذه الخدمة من الشريك؟", "desc2": "يتم حفظ معلوماتهم ويمكنك إعادة تنشيطها في أي وقت تريده."}, "inactivate": "الخدمة غير نشطة", "reactivate": "إعادة تنشيط الخدمة", "inactivateDesc": "تم إيقاف هذه الخدمة بتاريخ {date}. يمكنك الضغط على الزر أدناه لإعادة تفعيلها للشريك."}, "contract": {"title": "تفاصيل العقد", "label": {"contractNumber": "رقم العقد", "contractDocument": "مستند العقد"}, "placeholder": {"contractNumber": "رقم العقد"}}, "billingPreferences": {"title": "تفضيلات الفواتير", "label": {"billingCycle": "مدة الفاتورة", "creditLimit": "<PERSON><PERSON> الا<PERSON><PERSON><PERSON>ان", "invoiceType": "نوع الفاتورة", "preBilling": "الفوترة المسبقة", "endOfMonthBilling": "الفوترة نهاية الشهر"}, "placeholder": {"creditLimit": "4,500"}}, "rateCard": {"title": "بطاقة التعرفة", "createRateCard": "إنشاء بطاقة التعرفة", "noRateCard": "لا توجد بطاقة تعرفة لهذا الشريك", "viewDetails": "عرض التفاصيل", "label": {"rateType": "نوع التعرفة", "rateCardName": "اسم بطاقة التعرفة", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء"}, "placeholder": {"rateType": "نوع التعرفة", "rateCardName": "اسم بطاقة التعرفة"}, "hasNoCard": "لا يوجد بطاقة تعرفة"}, "paymentCoverage": {"title": "تغطية الشريك"}, "projects": {"title": "المشاريع", "label": {"projectID": "رقم المشروع", "projectName": "اسم المشروع"}, "placeholder": {"projectID": "رقم المشروع", "projectName": "اسم المشروع"}, "btn": {"addProject": "إضافة مشروع"}, "noProjects": "لا توجد مشاريع متاحة."}, "documents": {"uploading": "جاري التحميل...", "title": "الوثائق", "label": {"taxDocument": "مستند الضريبة", "crDocument": "مستند السجل التجاري"}, "btn": {"otherDocuments": "تحميل مستندات أخرى"}}, "updateDebtor": {"title": "تحديث الشريك"}, "companyType": {"corporate": "القطاع الخاص", "government": "حكومي", "semiGovernment": "شبه حكومي", "interCompany": "Inter Company"}, "billingCycle": {"30Days": "30 يومًا", "calendarDate": "التاريخ (نهاية الشهر)"}, "invoiceType": {"separate": "من<PERSON>صل", "consolidated": "<PERSON><PERSON><PERSON><PERSON>", "consolidatedByPO": "تم توحيده بواسطة PO"}}, "booking-details": {"Booking confirmation": "ت<PERSON><PERSON>ي<PERSON> الحجز", "Confirmation email will be sent to the provided address": "سيتم إرسال بريد إلكتروني للتأكيد إلى العنوان المقدم", "Confirmation email": "البريد الإلكتروني للتأكيد", "Contact email": "البريد الإلكتروني للتواصل", "Email address": "عنوان البريد الإلكتروني", "Customer Type": "نوع العميل", "Remarks": "ملاحظات", "Add remarks": "<PERSON><PERSON><PERSON>ظ<PERSON>ت", "VIP customer": "عميل VIP", "Add remarks here": "أ<PERSON><PERSON> ملاحظ<PERSON><PERSON> هنا", "Attach document": "إرفاق مستند", "Uploading": "جاري التحميل...", "Continue": "متابعة", "Save changes": "حفظ التغييرات", "No companies found": "لم يتم العثور على شركات", "Booking Details": "تفاصيل الحجز", "Search companies": "بحث الشركات...", "Select codecompany": "اختر الرمز/الشركة", "Company/code": "الشركة/الرمز", "Debtor PO": "أمر شراء المدين", "Debtor code": "<PERSON><PERSON><PERSON> المدين", "Vehicle Group": "مجموعة المركبة", "Authorization Matrix": "مصفوفة التفويض", "missingFields": "{fields} {fields, plural, one {مفقود} other {مفقودة}}", "Company payment coverage": "تغطية دفع الشركة", "Change company payment coverage": "تغيير تغطية دفع الشركة", "No coverage items available": "لا توجد عناصر تغطية متاحة.", "Edit": "تعديل", "Save": "<PERSON><PERSON><PERSON>", "Cancel": "إلغاء", "The changes will apply for the current booking only": "ستُطبق التغييرات للحجز الحالي فقط", "Group": "المجموعة", "Debtor": "المدين"}}