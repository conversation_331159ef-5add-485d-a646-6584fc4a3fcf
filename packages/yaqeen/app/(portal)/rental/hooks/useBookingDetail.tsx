import { Booking } from "@/api/contracts/booking/schema";
import { useCustomQuery } from "@/lib/hooks/use-query";

export function useBookingDetail(bookingId: string) {
  const {
    data: booking,
    isLoading,
    isError,
    error,
  } = useCustomQuery<Booking>(["booking", bookingId], `/next-api/booking/${bookingId}`, {
    enabled: !!bookingId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    booking,
    isLoading,
    isError,
    error,
  };
}
