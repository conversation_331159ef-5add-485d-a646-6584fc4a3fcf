"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "@/components/ui/sheet";
import { ChevronRight } from "lucide-react";
import { type Booking } from "@/api/contracts/booking/schema";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { Pos } from "@/api/contracts/booking/schema";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { DataTable } from "@/components/ui/data-table/data-table";
import { paymentColumns } from "../../../_components/payments-columns";
import { useEffect, useState } from "react";
import { Plus } from "@phosphor-icons/react/dist/ssr";
import AvailableDiscountsSheet from "./available-discounts-sidesheet";
import { SearchDiscount } from "./search-and-discount";
import { type AgreementPriceDetail, type Promotion } from "@/api/contracts/schema";
import PaymentFlow from "./PaymentFlow";
import CorporateDiscountsSheet from "./corporate-discounts-dialog";
import { DialogDescription } from "@radix-ui/react-dialog";
import TableSkeleton from "./table-skeleton";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import { startTransition } from "react";
import { remainingPriceAtom } from "../../atoms";
import { useAtomValue } from "jotai";
import RefundModal from "./refund-modal";
import { type BookingTransaction } from "@/api/contracts/payment-contract";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { calculatePriceDiscount, updateBookingDiscount } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";

export default function PaymentsInterface({
  payments,
  priceDetail,
  posResponse,
  promotions,
  corporatePromotions,
  bookingId,
  bookingNo,
  dropOffBranchId,
  dropOffTimestamp,
  vehicleGroupId,
  vehiclePlateNo,
  hasRefunds,
  pageName,
}: {
  payments: BookingTransaction[];
  priceDetail: Booking["priceDetail"] | AgreementPriceDetail;
  posResponse: Pos;
  promotions?: Promotion[];
  corporatePromotions?: Promotion[];
  bookingId?: number;
  bookingNo?: string;
  dropOffBranchId?: number;
  dropOffTimestamp?: number;
  vehicleGroupId?: number;
  vehiclePlateNo?: string;
  hasRefunds?: boolean;
  pageName?: string;
}) {
  const t = useTranslations("createAgreement");
  const tCommon = useTranslations("common");
  const { toast } = useToast();
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [refundModalOpen, setRefundModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [discountDetail, setDiscountDetail] = useState<Booking["priceDetail"]["discountDetail"]>(
    priceDetail?.discountDetail
  );
  const pathname = usePathname();

  // For handling redirect parameters
  const progress = useProgressBar();
  const [, setDropOffBranchId] = useQueryState("dropOffBranchId", { shallow: false });
  const [, setDropOffTimestamp] = useQueryState("dropOffTimestamp", { shallow: false });
  const [, setPlateNo] = useQueryState("plateNo", { shallow: false });
  const [, setVehicleGroupId] = useQueryState("vehicleGroupId", { shallow: false });
  const [discountCode] = useQueryState("discountCode", { shallow: false });
  const [, setQuoteId] = useQueryState("quoteId", { shallow: false });

  // Check for redirect from booking creation and set params
  useEffect(() => {
    if (typeof window !== "undefined") {
      const isRedirected = localStorage.getItem("isCreateBookingRedirect");

      if (isRedirected === "true" && priceDetail) {
        // Start progress indicator
        progress.start();

        startTransition(() => {
          // Set the query parameters if values are available
          if (dropOffBranchId) {
            void setDropOffBranchId(String(dropOffBranchId));
          }

          if (dropOffTimestamp) {
            void setDropOffTimestamp(String(dropOffTimestamp));
          }

          if (vehicleGroupId) {
            void setVehicleGroupId(String(vehicleGroupId));
          }

          if (vehiclePlateNo) {
            void setPlateNo(vehiclePlateNo);
          }

          // Remove the flag from localStorage after handling
          localStorage.removeItem("isCreateBookingRedirect");

          progress.done();
        });
      }
    }
  }, [priceDetail, progress, setDropOffBranchId, setDropOffTimestamp]);

  // Loading state effect
  useEffect(() => {
    setLoading(false);
  }, [payments?.length]);

  function showPaymentModel(): void {
    setPaymentModalOpen(!paymentModalOpen);
  }

  const updateBookignDiscount = async (code: Booking["priceDetail"]["discountDetail"]) => {
    setDiscountDetail(code);
    if (!bookingId) return;
    progress.start();
    const { data, error } = await calculatePriceDiscount({
      bookingId: String(bookingId),
      discountCode: String(code.promoCode),
    });

    if (error) {
      toast({ variant: "destructive", title: "Error", description: error.desc });
      progress.done();
      return;
    }

    const newQuoteId = data.quoteId;

    if (newQuoteId) {
      const bookingResp = await updateBookingDiscount(bookingId, newQuoteId);

      if (bookingResp.status !== 200) {
        console.error(bookingResp.body);
        toast({
          title: tCommon("errors.somethingWentWrong"),
          description: "desc" in bookingResp.body ? bookingResp.body.desc : tCommon("errors.genericError"),
          variant: "destructive",
        });
        progress.done();
        return;
      }

      startTransition(() => {
        void setQuoteId(newQuoteId);
        progress.done();
      });
    }
  };

  const remainingPrice = useAtomValue<string | null>(remainingPriceAtom);

  return (
    <div className="space-y-8">
      <PaymentFlow
        posResponse={posResponse}
        bookingId={bookingId}
        paymentModalOpen={paymentModalOpen}
        setPaymentModalOpen={setPaymentModalOpen}
        loading={loading}
        bookingNo={bookingNo}
        setLoading={setLoading}
      />

      {/* Refund Modal */}
      {bookingId && (
        <RefundModal
          bookingId={String(bookingId)}
          isOpen={refundModalOpen}
          onClose={() => setRefundModalOpen(false)}
          amount={Number(Math.abs(Number(remainingPrice) || 0))}
          sourceRoute={`close-agreements/${bookingId}/payment`}
        />
      )}

      {/* Discounts Section */}
      {corporatePromotions && promotions && (
        <section className="rounded-md border bg-white p-6 shadow-sm">
          <div className="mb-6 flex items-center justify-between md:flex-col lg:flex-col xl:flex-row">
            <h2 className="text-2xl font-semibold">{t("payment.discount.title")}</h2>
            <div className="flex gap-4">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <Plus className="h-4 w-4" />
                    {t("payment.discount.cta.corporateDiscount")}
                  </Button>
                </DialogTrigger>
                <DialogContent className=" max-h-96 p-0 sm:max-w-[425px]">
                  <DialogHeader className="p-4">
                    <DialogTitle>{t("payment.discount.dialogTitle")}</DialogTitle>
                    {discountCode && (
                      <DialogDescription className=" text-red-500">
                        {t("payment.discount.dialogDesc")}
                      </DialogDescription>
                    )}
                  </DialogHeader>
                  <CorporateDiscountsSheet
                    corporatePromotions={corporatePromotions}
                    setDiscountDetail={(code) => {
                      void updateBookignDiscount(code);
                    }}
                    setIsSheetOpen={setIsSheetOpen}
                  />
                </DialogContent>
              </Dialog>

              <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
                <SheetHeader className="sr-only">
                  <SheetTitle>{t("payment.discount.editProfile")}</SheetTitle>
                  <SheetDescription>{t("payment.discount.editProfileDesc")}</SheetDescription>
                </SheetHeader>
                <SheetTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    {t("payment.discount.cta.availableDiscount")}
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-full px-0 py-6  ring-0 sm:w-[400px] sm:max-w-full">
                  <AvailableDiscountsSheet
                    isDiscountApplied={!!discountDetail?.promoCode}
                    promotions={promotions}
                    setDiscountDetail={(code) => {
                      void updateBookignDiscount(code);
                    }}
                    setIsSheetOpen={setIsSheetOpen}
                  />
                </SheetContent>
              </Sheet>
            </div>
          </div>
          <div className="relative">
            <SearchDiscount
              setDiscountDetail={(code) => {
                void updateBookignDiscount(code);
              }}
              discountDetail={discountDetail}
              promotions={promotions}
              corporatePromotions={corporatePromotions}
            />
          </div>
        </section>
      )}

      <section className="rounded-md border bg-white p-6 shadow-sm">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-2xl font-semibold">{t("payment.discount.payment")}</h2>
          <div className="flex gap-2">
            {pathname.includes("close-agreements") && (
              <Button
                variant="outline"
                onClick={() => setRefundModalOpen(true)}
                disabled={hasRefunds || (Number(remainingPrice) || 0) >= 0}
              >
                {t("payment.discount.cta.issueRefund")}
              </Button>
            )}

            <Button
              variant="outline"
              className="gap-2"
              onClick={showPaymentModel}
              disabled={
                pageName === "createAgreement"
                  ? false
                  : pageName === "closeAgreement"
                    ? !!(Number(remainingPrice ?? "") <= 0)
                    : !!(Number(remainingPrice ?? "") <= 0)
              }
            >
              <Plus className="h-4 w-4" />
              {t("payment.discount.cta.collectPayment")}
            </Button>
          </div>
        </div>
        {loading ? (
          <TableSkeleton />
        ) : payments?.length > 0 ? (
          <DataTable
            searchPlaceholder=""
            columns={paymentColumns}
            data={{
              data: payments,
              total: payments.length ?? 0,
            }}
            emptyMessage={t("payment.discount.noPayments")}
          />
        ) : (
          <p className="p-6 text-center">{t("payment.discount.noPayments")}</p>
        )}
      </section>
    </div>
  );
}
