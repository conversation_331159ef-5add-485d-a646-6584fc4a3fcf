"use client";

import { cn } from "@/lib/utils";
import { type Vehicle, VehicleCard } from "./vehicle-card";

import { Button } from "@/components/ui/button";
import { Card, CardHeader } from "@/components/ui/card";
import { useAtom } from "jotai";
import { useHydrateAtoms } from "jotai/utils";
import { selectedVehicleAtom, type SelectedVehicleState, showSuggestedVehiclesAtom } from "../atoms";
import { VehiclesEmptyState } from "./vehicle-not-found";
import { useSearchParams, usePathname, useRouter, useParams } from "next/navigation";
import { startTransition, useEffect } from "react";
import { type Route } from "next";
import { useProgressBar } from "@/components/progress-bar";
import { useTranslations } from "next-intl";

interface SelectedVehicleProps {
  preSelectedVehicle: Vehicle | null;
  customerPreference: string;
  groupCode: string;
  isEmpty: boolean;
  isOnlyDowngrade: boolean;
  className?: string;
}

export default function SelectedVehicle({
  className,
  preSelectedVehicle,
  customerPreference,
  groupCode,
  isEmpty,
  isOnlyDowngrade,
}: SelectedVehicleProps) {
  const t = useTranslations("createAgreement");
  const params = useParams();
  const bookingId: string = (params.bookingId ?? "") as string;
  const agreementNo: string = (params.agreementNo ?? "") as string;

  const hydratedAtomKey = bookingId || agreementNo;

  useHydrateAtoms([
    [
      selectedVehicleAtom,
      (prevState: SelectedVehicleState | null) =>
        ({ ...prevState, [hydratedAtomKey]: preSelectedVehicle ?? {} }) as SelectedVehicleState,
    ],
    [showSuggestedVehiclesAtom, !preSelectedVehicle],
  ] as const);

  const [selectedVehicleState, setSelectedVehicleState] = useAtom<SelectedVehicleState | null>(selectedVehicleAtom);

  const [showSuggestedVehicles, setShowSuggestedVehicles] = useAtom(showSuggestedVehiclesAtom);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const progress = useProgressBar();

  useEffect(() => {
    if (preSelectedVehicle?.plateNo !== selectedVehicleState?.[hydratedAtomKey]?.plateNo) {
      setSelectedVehicleState((prevState) => {
        if (!selectedVehicleState?.[hydratedAtomKey] && !preSelectedVehicle) return prevState;
        return {
          ...prevState,
          [hydratedAtomKey]: selectedVehicleState?.[hydratedAtomKey] ?? preSelectedVehicle!,
        };
      });
    }
  }, [preSelectedVehicle, bookingId, setSelectedVehicleState]);

  useEffect(() => {
    if (!selectedVehicleState?.[hydratedAtomKey]?.plateNo) return;

    progress?.start();
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      const plateNo = params.get("plateNo");
      const vehicleGroupId = params.get("vehicleGroupId");
      if (!plateNo || !vehicleGroupId || plateNo !== (selectedVehicleState?.[hydratedAtomKey]?.plateNo ?? "")) {
        params.set("plateNo", selectedVehicleState?.[hydratedAtomKey]?.plateNo ?? "");
        if (selectedVehicleState?.[hydratedAtomKey]?.model?.groupResponse?.id !== undefined) {
          params.set("vehicleGroupId", selectedVehicleState[hydratedAtomKey].model.groupResponse.id.toString());
        }
        router.replace(`${pathname}?${params.toString()}` as Route);
      }
      progress?.done();
    });
  }, [selectedVehicleState?.[hydratedAtomKey], searchParams, pathname, router]);

  const vehicleCard =
    selectedVehicleState?.[hydratedAtomKey] && Object.keys(selectedVehicleState[hydratedAtomKey]).length ? (
      <VehicleCard
        vehicleDetails={selectedVehicleState?.[hydratedAtomKey] ?? null}
        renderActionButton={() =>
          !showSuggestedVehicles ? (
            <Button variant="outline" size="sm" className="rounded-lg" onClick={() => setShowSuggestedVehicles(true)}>
              {t("assignVehicle.cta.change")}
            </Button>
          ) : null
        }
        isSelected
      />
    ) : (
      <VehiclesEmptyState
        customerPreference={customerPreference}
        groupCode={groupCode}
        hasRecommendedVehicle={!!preSelectedVehicle}
        isEmpty={isEmpty}
        isOnlyDowngrade={isOnlyDowngrade}
      />
    );

  return (
    <Card className={cn("!p-0", className)}>
      <CardHeader className="border-b p-4">
        <h2 className="text-lg font-semibold">{t("assignVehicle.selectedVehicle")}</h2>
      </CardHeader>

      {vehicleCard}
    </Card>
  );
}
