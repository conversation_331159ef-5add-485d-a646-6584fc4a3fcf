import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import RadioChip from "@/components/ui/radio-chip";
import { Separator } from "@/components/ui/separator";
import { freeUpgradeDataAtom, type UpgradignVehicleAtom, upgradingVehicleAtom } from "../atoms";
import { useAtom } from "jotai";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { VehicleUpgradeReasons } from "./upgrade-reasons";
import { useActionState } from "react";
import { type Vehicle } from "./vehicle-card";
import { useState } from "react";
import { Loader2 } from "lucide-react";
import { useQueryState } from "nuqs";
import { useLocale, useTranslations } from "next-intl";

type FormState = {
  upgradeType: string;
  approve: boolean;
  offerFreeVehicleUpgrade: boolean;
  identifier: string;
};

type VehicleUpgradeProps = {
  open: boolean;
  onSelect: (type: string, selectedVehicle: Vehicle) => void;
  onClose: () => void;
};

export const VehicleUpgrade = ({ open, onSelect, onClose }: VehicleUpgradeProps) => {
  const t = useTranslations("createAgreement");
  const locale = useLocale();
  const [upgradingVehicle, setUpgradingVehicle] = useAtom<UpgradignVehicleAtom>(upgradingVehicleAtom);
  const [, setFreeUpgradeDataAtom] = useAtom(freeUpgradeDataAtom);
  const [loading, setLoading] = useState(false);
  const [, setOfferFreeVehicleUpgrade] = useQueryState<boolean>("offerFreeVehicleUpgrade", {
    parse: (value) => value === "true",
    serialize: (value) => value.toString(),
    shallow: false,
  });
  const [, setFormState] = useActionState<FormState, FormData>(
    async (prevState, formData) => {
      debugger;
      await setLoading(true);
      // Process form data (this is client-side)
      const approve = formData.get("approve") === "on";
      const identifier = formData.get("identifier") as string;
      const reasonText = formData.get("reasonText") as string;
      const upgradeType = formData.get("upgradeType") as string;

      const vehicle = { ...upgradingVehicle?.vehicle, plateNo: upgradingVehicle?.vehicle?.plateNo || "" };
      if (upgradeType === "free_upgrade") {
        setFreeUpgradeDataAtom({
          reason: identifier,
          reasonText,
        });
      }

      debugger;
      onSelect(upgradingVehicle?.type || "", vehicle as Vehicle);
      setLoading(false);
      return { approve, identifier, offerFreeVehicleUpgrade: upgradeType === "free_upgrade", upgradeType };
    },
    {
      approve: false,
      offerFreeVehicleUpgrade: false,
      identifier: "",
      upgradeType: upgradingVehicle?.type || "",
    }
  );

  return (
    <>
      <Dialog
        open={open}
        onOpenChange={(open) => {
          if (!open) {
            void setOfferFreeVehicleUpgrade(false);
            onClose();
          }
        }}
      >
        <DialogContent className="p-0 sm:max-w-md" dir={locale === "ar" ? "rtl" : "ltr"}>
          <DialogHeader className="p-4">
            <DialogTitle>
              {t("assignVehicle.upgradeModel.title")}{" "}
              {`${locale === "ar" ? upgradingVehicle?.vehicle.model?.make?.name?.ar : upgradingVehicle?.vehicle.model?.make?.name?.en} ${locale === "ar" ? upgradingVehicle?.vehicle.model?.name?.ar : upgradingVehicle?.vehicle.model?.name?.en} ${upgradingVehicle?.vehicle.modelYear}`}
            </DialogTitle>
            <DialogDescription>{t("assignVehicle.upgradeModel.description")}</DialogDescription>
          </DialogHeader>
          <Separator />

          <form action={setFormState}>
            <fieldset className="max-h-100">
              <RadioChip
                name="upgradeType"
                options={[
                  {
                    value: "paid_upgrade",
                    label: `${t("assignVehicle.upgradeModel.paidUpgrade")} (+SAR ${upgradingVehicle?.vehicle?.offer?.bookingPriceDifference?.toLocaleString() ?? 0})`,
                  },
                  { value: "free_upgrade", label: `${t("assignVehicle.upgradeModel.freeupgrade")}` },
                ]}
                onChange={(event) => {
                  const type = (event.target as HTMLInputElement).value;
                  if (upgradingVehicle) {
                    setUpgradingVehicle({ ...upgradingVehicle, type });
                    void setOfferFreeVehicleUpgrade(!!(type === "free_upgrade"));
                  }
                }}
              />
            </fieldset>
            <Separator />
            {upgradingVehicle?.type === "free_upgrade" && (
              <fieldset>
                <section>
                  <header className="p-4">
                    <h3 className="text-lg font-semibold">{t("assignVehicle.upgradeModel.reasonForUpgrade")}</h3>
                  </header>
                  <VehicleUpgradeReasons />
                </section>
                <Separator className="mt-4" />
                <section className="flex items-center p-4">
                  <Checkbox name="approve" required />
                  <Label className="ml-3">{t("assignVehicle.upgradeModel.approvalText")}</Label>
                </section>
                <Separator />
              </fieldset>
            )}
            <DialogFooter className="gap-x-2 p-4 sm:justify-end">
              <Button
                type="button"
                onClick={() => {
                  void setOfferFreeVehicleUpgrade(false);
                  onClose();
                }}
                variant="outline"
              >
                {t("assignVehicle.upgradeModel.negativeCta")}
              </Button>
              <Button type="submit" disabled={!upgradingVehicle?.type}>
                {t("assignVehicle.upgradeModel.successCta")} {loading && <Loader2 className="h-4 w-4" />}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};
