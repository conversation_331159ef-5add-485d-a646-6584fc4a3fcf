{
  /* Booking details page */
}
import { api } from "@/api";
import AgreementDetailAndRentalRate from "../../close-agreements/[agreementNo]/booking-details/booking-detail-and-rental";
import { format } from "date-fns";

import { notFound, redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { BookingDetailAndRentalRateSkeleton } from "../_components/skeletons/booking-detail-rental-skeleton";
import { LoyaltySkeleton } from "../_components/skeletons/loyalty-program-skeleton";
import { PricingBreakdownSkeleton } from "../_components/skeletons/pricing-breakdown-skeleton";
import Payments from "./booking-details//payments";
import { bookingSearchParamsCache } from "./booking-details/bookingSearchParams";
import LoyaltyProgram from "./booking-details/loyalty-program";

import { Calendar, Plus, Warning, DotsThree } from "@phosphor-icons/react/dist/ssr";

import type { Booking, CalculatePrice } from "@/api/contracts/booking/schema";
import type { AgreementInvoice, TajeerAgreement } from "@/api/contracts/schema";
import { getPermissions } from "@/app/(portal)/utils";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardTitle, CardHeader } from "@/components/ui/card";
import { capitalize } from "lodash-es";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { PencilSimple } from "@phosphor-icons/react/dist/ssr";
import Link from "next/link";
import { InspectionReport } from "../../close-agreements/[agreementNo]/inspection-details/_components/InspectionReport";
import { CancelBooking } from "../_components/cancel-booking";
import { type Vehicle, VehicleCard } from "./assign-a-vehicle/_components/vehicle-card";
import { CopyLinkButton } from "./_components/CopyLinkButton";

import StaticPricingBreakdown from "./static-pricing-breakdown";
import StaticBookingDetailAndRentalRate from "./booking-details/static-booking-detail-and-rental";

import BookingInvoices from "./_components/booking-invoices";
import ValidateCode from "./authorization/_components/ValidateCode";
import IbanDropdown from "@/app/(portal)/rental/all-bookings/_components/iban-dropdown";
import StaticAgreementPricingBreakdown from "./static-agreement-price-breakdown";
import { getTranslations } from "next-intl/server";
import SidesheetWrapper from "./_components/sidesheet-wrapper";
import { getUserLocale } from "@/services/locale";
import PageHeader from "./_components/page-header";
import { enUS, arSA } from "date-fns/locale";
import clsx from "clsx";
import { PaymentRefundButton } from "./_components/payment-refund";
import PaymentLinks from "./_components/payment-links";
import { ReplacementHistoryButton } from "@/app/(portal)/rental/_components/replacement-history";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string; id: number }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const t = await getTranslations("bookingDetail");
  const bookingT = await getTranslations("bookings");
  const discountT = await getTranslations("discount");
  const closeAgreeT = await getTranslations("closeAgreement");
  const locale = await getUserLocale();
  const nLocale = locale === "ar" ? arSA : enUS;
  const { dropOffBranchId, dropOffTimestamp, agreementNo } = await bookingSearchParamsCache.parse(searchParams);
  const { bookingId, id } = await params;
  let editBookingAllowed = false;

  try {
    const permissions = await getPermissions();
    editBookingAllowed = permissions?.nested?.booking?.bookings === "write";
  } catch (e) {
    console.log(e);
  }

  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(bookingId),
    },
  });

  let agreement: AgreementInvoice | null = null;
  let priceResponse: CalculatePrice | null = null;
  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const booking: Booking = bookingResponse.body;

  let finalAgreementNumber = "0";
  let paymentTransactionsData, hasRefunds, isValidPaymentMode;
  if (["ONGOING", "COMPLETED", "LATE_RETURN"].includes(booking?.status ?? "")) {
    finalAgreementNumber = agreementNo;
    if (finalAgreementNumber === "0") {
      finalAgreementNumber = booking?.agreementNo ?? "0";
    }
    if (finalAgreementNumber === "0") {
      notFound();
    }
    const agreementResponse = await api.booking.getAgreement({
      params: {
        agreementNo: finalAgreementNumber.toString(),
      },
    });

    if (agreementResponse?.status === 404) {
      notFound();
    }

    if (agreementResponse.status !== 200) {
      throw new Error("Failed to fetch booking details");
    }
    agreement = agreementResponse.body;
    const priceCalculatorResponse = await api.bookingDetails.calculateAgreementPrice({
      params: {
        agreementNo: finalAgreementNumber.toString(),
      },
    });
    priceResponse = priceCalculatorResponse.body as CalculatePrice;
  } else {
    const transactionsResponse = await api.payment.getBookingTransactions({
      params: {
        bookingId,
      },
      query: {
        notInitiatedFor: "SECURITY_DEPOSIT_AUTHORIZATION",
      },
    });

    if (transactionsResponse?.status !== 200) {
      throw new Error(`Error: ${transactionsResponse.status}`);
    }

    const { data } = transactionsResponse.body;
    paymentTransactionsData = data;
    isValidPaymentMode = !paymentTransactionsData.every((payment) => payment.mode === "ONLINE");
    hasRefunds = paymentTransactionsData.some((transaction) => transaction.type.toLowerCase().includes("refund"));
  }

  const getTajeerContracts = await api.tajeer.getAllTajeerAgreements({
    query: {
      referenceNumber: booking.bookingNo ?? "",
      size: 100,
    },
  });
  let tajeerContracts: TajeerAgreement[] = [];
  if (getTajeerContracts.status === 200) {
    tajeerContracts = getTajeerContracts?.body?.data ?? [];
  }
  const isAuthorized = tajeerContracts.some((contract) => contract.status === "SUCCESS" && contract.type === "TAJEER");
  const isTammAuthorized = tajeerContracts.some(
    (contract) => contract.status === "SUCCESS" && contract.type === "TAMM"
  );

  const isAuthorizedForTajeer = isAuthorized || isTammAuthorized;

  if (!dropOffBranchId && !dropOffTimestamp) {
    const defaultSearchParams = await searchParams;
    defaultSearchParams.dropOffBranchId = booking.dropOffBranchId?.toString() ?? "";
    defaultSearchParams.dropOffTimestamp = booking.dropOffDateTime?.toString() ?? "";
    const newSearchParams = new URLSearchParams();
    Object.entries(defaultSearchParams).forEach(([key, value]) => {
      if (value) {
        newSearchParams.append(key, value as string);
      }
    });

    redirect(`?${newSearchParams.toString()}`, RedirectType.replace);
  }

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const BOOKING_DETAIL_KEY = `${suspenseKey}_booking_detail`;
  const PAYMENTS_KEY = `${suspenseKey}_payments`;
  const LOYALTY_PROGRAM_KEY = `${suspenseKey}_loyalty_program`;
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;

  const { priceDetail } = booking;

  const discount = {
    discount_code: priceDetail.discountDetail?.promoCode ?? "",
    percentage: priceDetail.discountDetail?.totalDiscount
      ? // @ts-expect-error TODO: Fix this
        `${new Intl.NumberFormat(locale, { style: "currency", currency: "SAR" }).format(priceDetail.discountDetail?.totalDiscount)} (${priceDetail.discountDetail?.discountPercentage}%)`
      : "",
  };

  const tajeerLink =
    process.env.NODE_ENV === "production" ? "https://tajeer.tga.gov.sa/" : "https://tajeerstg.tga.gov.sa/#/";

  const refund = await api.refund.searchRefunds({
    query: {
      refundRequestStatus: "REQUESTED",
      bookingNo: booking.bookingNo ?? "",
    },
  });

  if (refund.status !== 200) {
    throw new Error("Failed to fetch refund requests");
  }

  const refundData = refund.body.data.pop();

  // Get driver UID for the DriverProfileSheet component
  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const shouldShowRefund =
    booking.status === "CANCELLED" && !hasRefunds && isValidPaymentMode && Number(booking.driverPaidAmount) > 0;

  return (
    <>
      <div className="flex flex-col pb-16 lg:min-h-screen">
        <PageHeader
          pageName={`${t("Booking")} ${booking.bookingNo}`}
          booking={booking}
          source={booking?.source ?? ""}
          bookingType={booking?.bookingType ?? `Local`}
          bookingId={bookingId}
          branchId={id}
          bookingNumber={booking.bookingNo}
        >
          <div className={`${locale === "ar" ? "mr-auto" : "ml-auto"} flex gap-2`}>
            {["ONGOING", "LATE_RETURN"].includes(booking?.status ?? "") && (
              <>
                {/* TODO: disable temporary until extend booking ticket is tested by QA  */}
                {/* <Link href={`/rental/branches/${id}/close-agreements/${finalAgreementNumber}/extend-booking`}> */}
                <Button variant="outline" className="flex items-center gap-2 rounded-md" disabled>
                  <Calendar className="h-4 w-4" /> {t("cta.extend")}
                </Button>
                {/* </Link> */}
                <Link href={`/rental/branches/${id}/close-agreements/${finalAgreementNumber}/inspection-details`}>
                  <Button variant="default" className="flex items-center gap-2 rounded-md">
                    <Calendar className="h-4 w-4" /> {t("cta.close")}
                  </Button>
                </Link>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div className="flex items-center justify-end gap-x-1">
                      <Button variant="outline">
                        <DotsThree className="h-4 w-4" />
                      </Button>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <Link
                      className="flex items-center gap-2 p-2"
                      href={`/rental/branches/${id}/replace-vehicle/${finalAgreementNumber}/inspection-details`}
                    >
                      <Calendar className="h-4 w-4" /> Replace Vehicle
                    </Link>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
            {booking.status === "COMPLETED" && (
              <>
                <BookingInvoices bookingNo={booking.bookingNo ?? ""} />
                <CopyLinkButton />
              </>
            )}
            {booking.status === "UPCOMING" && (
              <CancelBooking
                bookingRef={booking.referenceNo ?? ""}
                disabled={isAuthorizedForTajeer}
                isOnlinePayment={!isValidPaymentMode}
              />
            )}
            {(booking.status === "CANCELLED" || booking.status === "NO_SHOW") && (
              <>
                {shouldShowRefund && (
                  <PaymentRefundButton
                    bookingId={bookingId}
                    hasRefunds={hasRefunds ?? false}
                    show={shouldShowRefund}
                    amount={Number(booking.driverPaidAmount) ?? 0}
                  />
                )}
                <CopyLinkButton />
              </>
            )}
            {booking.status === "UPCOMING" && (
              <>
                {/* <Button variant="outline" className="flex items-center gap-2 rounded-md">
                  <PencilSimple className="h-4 w-4" /> Edit Booking
                </Button> */}
                <Link href={`/rental/branches/${id}/bookings/${bookingId}/booking-details`}>
                  <Button variant="default" className="flex items-center gap-2 rounded-md">
                    <Plus className="h-4 w-4" /> {t("cta.start")}
                  </Button>
                </Link>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="flex items-center gap-2 rounded-md">
                      ...
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[200px]">
                    {editBookingAllowed ? (
                      <DropdownMenuItem disabled className="flex cursor-pointer items-center gap-2">
                        <PencilSimple className="h-4 w-4" />
                        {t("cta.edit")}
                      </DropdownMenuItem>
                    ) : null}
                    <CopyLinkButton variant="dropdown" />
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
            {refundData && (
              <IbanDropdown className="h-auto" bookingNo={booking.bookingNo ?? ""} ibanLink={refundData?.link ?? ""} />
            )}
          </div>
        </PageHeader>
        <div className="container my-6 px-16">
          <main className="w-full space-y-6">
            <section className="flex gap-x-10">
              <div className="col-span-8 flex min-w-[768px] flex-col gap-y-6">
                {booking.status === "CANCELLED" && booking.cancellationDetails && (
                  <Alert variant="destructive" className="my-0 bg-red-50">
                    <Warning weight="bold" className="h-4 w-4" />
                    <AlertTitle className="flex gap-2">
                      <div>{t("cancelBy")}:</div>
                      <div className={clsx("flex gap-2", locale === "ar" ? "flex-row-reverse" : "")}>
                        <div>{booking.cancellationDetails.employeeName || booking.cancellationDetails.cancelledBy}</div>
                        <div>{t("on")} </div>
                        <div>
                          {format(new Date((booking.cancellationDetails.cancelledOn || 0) * 1000), "dd/MM/yyyy", {
                            locale: nLocale,
                          })}
                        </div>
                      </div>
                    </AlertTitle>
                    {booking.cancellationDetails.reason && (
                      <AlertDescription>
                        {t("reason")}: {booking.cancellationDetails.reason}
                      </AlertDescription>
                    )}
                  </Alert>
                )}
                <Suspense key={BOOKING_DETAIL_KEY} fallback={<BookingDetailAndRentalRateSkeleton />}>
                  {["ONGOING", "COMPLETED", "LATE_RETURN"].includes(booking?.status ?? "") ? (
                    <AgreementDetailAndRentalRate agreement={agreement!} />
                  ) : (
                    <StaticBookingDetailAndRentalRate booking={bookingResponse.body} />
                  )}
                </Suspense>
                {["ONGOING", "COMPLETED"].includes(booking?.status ?? "") && (
                  <Card>
                    <CardTitle className="flex items-center justify-between  border-b py-4 pr-4">
                      <div className="px-4 py-2 text-lg font-medium">{t("assignedVehicle")}</div>
                      <ReplacementHistoryButton agreementNo={agreement.agreementNo} />
                    </CardTitle>
                    <CardContent>
                      <VehicleCard
                        vehicleDetails={(agreement?.assignedVehicle?.vehiclePlateInfo as unknown as Vehicle) ?? null}
                      />
                    </CardContent>
                    <Separator />
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="item-1">
                        <AccordionTrigger className="w-full px-4">{closeAgreeT("Inspection Report")}</AccordionTrigger>
                        <AccordionContent className="p-2">
                          {priceResponse && agreement && (
                            <>
                              <InspectionReport displayOnly priceResponse={priceResponse} agreement={agreement} />
                            </>
                          )}
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </Card>
                )}
                {["ONGOING", "COMPLETED"].includes(booking?.status ?? "") && (
                  <ValidateCode
                    tajeerLink={tajeerLink}
                    bookingNo={agreement!.bookingNo}
                    tajeerContracts={tajeerContracts}
                    disableSecurityDeposit
                    displayOnly
                  />
                )}
                <Suspense key={PAYMENTS_KEY} fallback={<LoyaltySkeleton />}>
                  <Payments bookingId={bookingId} />
                </Suspense>

                {/* Payment links here */}
                <Suspense fallback={<LoyaltySkeleton />}>
                  <PaymentLinks bookingNo={booking.bookingNo ?? ""} />
                </Suspense>

                <Suspense key={LOYALTY_PROGRAM_KEY} fallback={<LoyaltySkeleton />}>
                  <LoyaltyProgram bookingId={bookingId} />
                </Suspense>
                {/* Discount Card */}
                {discount.discount_code !== "" ? (
                  <Card className="flex flex-col shadow">
                    <CardHeader className="px-4">
                      <CardTitle className="text-lg font-bold">{t("discount")}</CardTitle>
                    </CardHeader>
                    <Separator />
                    <CardContent className="flex w-full flex-col items-center p-0 ">
                      <div className="flex w-full p-4 text-sm">
                        {Object.entries(discount).map(([key, value], index) => {
                          // @ts-expect-error TODO: Fix this
                          const _discount = discountT(capitalize(key.replace(/_/g, " ")));
                          return (
                            <div key={index} className="flex w-1/3 flex-col gap-y-2">
                              <span className="text-slate-500">{_discount}</span>
                              <span className=" text-slate-900 ">{value || "N/A"}</span>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                ) : null}
              </div>
              <div className="col-span-4 grow">
                {["UPCOMING", "CANCELLED", "NO_SHOW"].includes(booking?.status ?? "") ? (
                  <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
                    <StaticPricingBreakdown booking={bookingResponse.body}>
                      {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : null}
                    </StaticPricingBreakdown>
                  </Suspense>
                ) : null}
                {["COMPLETED", "ONGOING", "LATE_RETURN"].includes(booking?.status ?? "")
                  ? agreement && (
                      <StaticAgreementPricingBreakdown booking={agreement}>
                        {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : null}
                      </StaticAgreementPricingBreakdown>
                    )
                  : null}
              </div>
            </section>
          </main>
        </div>
      </div>
    </>
  );
}
