"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Globe, User } from "@phosphor-icons/react/dist/ssr";
import type { Route } from "next";
import { useParams, usePathname } from "next/navigation";
import { BookingNav } from "./booking-nav";
import { EXTEND_AGREEMENT_URL } from "./constants";
import { mapBookingSource, mapBookingType } from "@/lib/maper";
import { type Booking } from "@/api/contracts/booking/schema";
import { useLocale, useTranslations } from "next-intl";

interface PageTitleProps {
  aggregatorName: string;
  bookingType: string;
  agreementNo: string;
  source: string;
  booking?: Booking;
}

export default function PageTitle({ aggregatorName, bookingType, agreementNo, source, booking }: PageTitleProps) {
  const pathname = usePathname();
  const commonT = useTranslations("common");
  const t = useTranslations("replaceVehicle");
  const params = useParams<{ id: string }>();
  const isExtendAgreementPage = pathname.includes(EXTEND_AGREEMENT_URL);
  const locale = useLocale();

  return (
    <section className="border-b bg-slate-50">
      <div className="container flex w-full flex-col self-stretch px-24">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/">{commonT("home")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${params.id}/bookings` as Route}>
                  {t("myBookings")}
                </ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{t("title")}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex w-full items-start justify-between py-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-medium tracking-tight">{t("title")}</h1>
            <div className="flex items-center gap-2">
              <span className="text-slate-700">
                {t("agreementNo")} {agreementNo}
              </span>
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                <User className="size-3" />
                {mapBookingType(
                  bookingType,
                  booking?.priceDetail?.discountDetail?.promoCode,
                  booking?.debtorName,
                  locale
                )}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                <Globe className="size-3" />
                {mapBookingSource(source, aggregatorName, locale)}
              </Badge>
            </div>
          </div>
        </div>
        {!isExtendAgreementPage && <BookingNav agreementNo={agreementNo} />}
      </div>
    </section>
  );
}
