"use client";

import {
  closeAgreement,
  fetchInvoiceStatus,
  generateInvoice,
  initVehicleReplacement,
  retryAuthorization,
} from "@/lib/actions";
import { ContinueButton } from "@/components/ContinueButton";
import { Button } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";
import { XCircle, CaretLeft, CaretRight, Confetti, Printer } from "@phosphor-icons/react/dist/ssr";
import { useAtom } from "jotai";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { useActionState, useMemo, useState, useEffect } from "react";
import { type Route } from "next";
import { getNextTabUrl } from "./constants";
import { atomWithBookingNav } from "./atoms";
import { Di<PERSON>, DialogContent, DialogFooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import type { ClosingPrice } from "@/api/contracts/booking/schema";
import { AlertTriangle, Loader2 } from "lucide-react";
import { type InvoiceSearchResponse, type InvoiceStatusResponse } from "@/api/contracts/booking/invoice-contract";
import { type Authorization, type CloseAgreementResponse } from "@/api/contracts/booking/booking-contract";
import { format } from "date-fns"; // Optional: Use date-fns for formatting

const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
  return format(date, "dd/MM/yyyy - HH:mm:ss"); // Format using date-fns
};

export function ActionsBar({
  closingPriceResponse,
  className,
  agreementNo,
  vehicleStatus,
  successCtaDisabled,
}: {
  closingPriceResponse?: ClosingPrice;
  className?: string;
  agreementNo: string;
  vehicleStatus?: string;
  successCtaDisabled?: boolean;
}) {
  const t = useTranslations("replaceVehicle");
  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const params = useParams();
  const bookingId = Number(params.bookingId);
  const id = (params?.id as string) ?? "";
  const bookingNavAtom = useMemo(() => atomWithBookingNav(), [bookingId]);
  const [,] = useAtom(bookingNavAtom);
  // const [, updateNavItems] = useAtom(bookingNavAtom);
  const nextTabItemUrl = getNextTabUrl(pathname, agreementNo, id);

  const isAuthorizationPage = pathname.includes("/authorization");
  const isVehicleAssignmentPage = pathname.includes("/assign-vehicle");

  const [, formAction] = useActionState(async () => {
    const currentSearchParams = searchParams.toString();

    return router.push((nextTabItemUrl + "?" + currentSearchParams) as Route);
  }, null);

  const handleBack = () => {
    const currentSearchParams = searchParams.toString();
    router.back();
    if (currentSearchParams) {
      router.replace((window.location.pathname + "?" + currentSearchParams) as Route);
    }
  };

  const handleSaveExit = () => {
    router.push(`/rental/branches/${id}/bookings`);
  };

  const handleCloseAgreement = async () => {
    // setIsLoadingCloseAgreement(true); // Set loading state to true
    // try {
    //   const response = await closeAgreement(agreementNo, closingPriceResponse?.quoteId ?? "", vehicleStatus ?? "READY");
    //   if (Number(response.status) === 200 || Number(response.status) === 201) {
    //     const result: CloseAgreementResponse = response.body as CloseAgreementResponse;
    //     setCloseAgreementResp(result);
    //   } else {
    //     toast({
    //       title: "Uh, something went wrong.",
    //       description: "desc" in response.body ? response.body.desc.split(".").join(" ") : "An error occurred",
    //       variant: "destructive",
    //     });
    //   }
    // } catch (error) {
    //   console.error("Error closing agreement:", error);
    //   toast({
    //     title: "Error",
    //     description: "An error occurred while closing the agreement.",
    //     variant: "destructive",
    //   });
    // } finally {
    //   setIsLoadingCloseAgreement(false); // Reset loading state
    //   setConfirmationModalOpen(false); // Close confirmation modal
    // }
  };

  const handleContinue = async () => {
    if (isVehicleAssignmentPage) {
      try {
        debugger;
        const replacementVehiclePlateNo = searchParams.get("plateNo");
        const reasonType = searchParams.get("replaceReason");
        const agreementNo = typeof params.agreementNo === "string" ? params.agreementNo : "";
        if (!agreementNo) return;
        console.log(
          `\n HERE is payload: ${JSON.stringify({
            replacementVehiclePlateNo: replacementVehiclePlateNo ?? "",
            replacement: true,
            reasonType: reasonType ?? "",
            replacedVehicleNextStatus: "READY",
          })} \n`
        );
        const response = await initVehicleReplacement(agreementNo ?? "", {
          replacementVehiclePlateNo: replacementVehiclePlateNo ?? "",
          replacement: {
            reasonType: reasonType ?? "",
          },
          replacedVehicleNextStatus: "READY",
        });
        debugger;
        if (Number(response.status) === 200 || Number(response.status) === 201) {
          const result = response.body;
        } else {
          toast({
            title: "Uh, something went wrong.",
            description: "desc" in response.body ? response.body.desc.split(".").join(" ") : "An error occurred",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error closing agreement:", error);
        toast({
          title: "Error",
          description: "An error occurred while closing the agreement.",
          variant: "destructive",
        });
      } finally {
      }
    }
  };

  return (
    <footer
      className={cn(
        "flex flex-wrap items-center rounded-lg border border-solid border-slate-200 bg-white text-sm font-medium leading-none text-slate-900 shadow",
        className
      )}
    >
      <div className="my-auto flex w-full  flex-1 shrink basis-0 flex-wrap items-center gap-4 self-stretch p-4">
        {!pathname.includes("/booking-details") && (
          <Button variant="outline" onClick={handleBack} className="flex items-center gap-2">
            <CaretLeft className="h-4 w-4" />
            {t("cta.back")}
          </Button>
        )}
        <Button variant="outline" onClick={handleSaveExit}>
          {t("cta.exit")}
        </Button>
      </div>
      <form action={formAction} className="my-auto flex items-center gap-4 self-stretch p-4">
        {!isAuthorizationPage ? (
          <ContinueButton className="flex items-center" onClick={handleContinue}>
            <span>{t("cta.continue")} </span>
            <CaretRight className="mx-1 h-4 w-4" />
          </ContinueButton>
        ) : (
          <ContinueButton
            type="button"
            disabled={successCtaDisabled}
            onClick={async (e) => {
              e.preventDefault();
              // setConfirmationModalOpen(true); // Open confirmation modal
            }}
          >
            {t("cta.submit")}
          </ContinueButton>
        )}
      </form>
    </footer>
  );
}

export default ActionsBar;
