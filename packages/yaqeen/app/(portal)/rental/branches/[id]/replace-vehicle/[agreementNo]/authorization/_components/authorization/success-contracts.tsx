"use client";

import { Badge } from "@/components/ui/badge";
import { CheckCircle2 } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { TajeerAgreement } from "@/api/contracts/schema";
import AuthCard from "./auth-card";
import { useTranslations } from "next-intl";

export default function SuccessContracts({ contracts }: { contracts: TajeerAgreement[] }) {
  const t = useTranslations("authorization");
  return (
    <section className="p-4">
      <header className="pb-4">
        <h3 className="flex items-center gap-2 text-base font-semibold">
          {contracts && contracts.length && <span className="capitalize">{contracts[0]?.type?.toLowerCase()}</span>}
          <Badge variant="secondary" className="h-6 bg-green-100 text-green-800">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            {t("statuses.authorized")}
          </Badge>
        </h3>
      </header>
      {[...contracts]
        .sort((a, b) => (b.createdOn || 0) - (a.createdOn || 0))
        .map((contract) => (
          <AuthCard key={contract.id} contract={contract} />
        ))}
    </section>
  );
}
