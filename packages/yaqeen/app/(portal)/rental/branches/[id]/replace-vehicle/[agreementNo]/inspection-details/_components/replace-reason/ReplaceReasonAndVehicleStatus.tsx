"use client";

import { useQueryStates, parseAsString } from "nuqs";
import { startTransition } from "react";
import { useProgressBar } from "@/components/progress-bar";
import { ReplaceReason } from "./ReplaceReason";
import { VehicleStatusCard } from "./VehicleStatusCard";

function ReplaceReasonAndVehicleStatus() {
  const progress = useProgressBar();

  const [{ replaceReason, vehicleStatus }, setQueryParams] = useQueryStates(
    {
      replaceReason: parseAsString.withDefault(""),
      vehicleStatus: parseAsString.withDefault(""),
    },
    {
      shallow: false,
      clearOnDefault: false,
    }
  );

  const handleReplaceReasonChange = (newReason: string) => {
    progress.start();
    startTransition(() => {
      // When replace reason changes, we might need to auto-select vehicle status
      let newVehicleStatus = vehicleStatus;

      // Auto-select workshop for accident/maintenance
      if (newReason === "MAJOR_DAMAGE" || newReason === "MAINTENANCE") {
        newVehicleStatus = "WORKSHOP_TRANSFER";
      }

      void setQueryParams({
        replaceReason: newReason,
        vehicleStatus: newVehicleStatus,
      });
      progress.done();
    });
  };

  const handleVehicleStatusChange = (newStatus: string) => {
    progress.start();
    startTransition(() => {
      void setQueryParams({ vehicleStatus: newStatus });
      progress.done();
    });
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex-1">
        <ReplaceReason value={replaceReason} onValueChange={handleReplaceReasonChange} />
      </div>
      <div className="flex-1">
        <VehicleStatusCard
          value={vehicleStatus}
          onValueChange={handleVehicleStatusChange}
          replaceReason={replaceReason}
        />
      </div>
    </div>
  );
}

export default ReplaceReasonAndVehicleStatus;
export { ReplaceReasonAndVehicleStatus };
