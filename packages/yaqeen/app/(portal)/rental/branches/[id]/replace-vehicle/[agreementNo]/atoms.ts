import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { navItems as initialNavItems, type NavItem } from "./constants";

export function atomWithBookingNav() {
  const baseAtom = atomWithStorage<NavItem[]>(`replace-vehicle-nav-items`, initialNavItems);

  const derivedAtom = atom(
    (get) => get(baseAtom),
    (get, set, updates: string) => {
      const currentNavItems = get(baseAtom);
      const updatedNavItems = currentNavItems.map((item) => {
        if (updates.includes(item.href)) {
          return { ...item, completed: true };
        }
        return item;
      });
      set(baseAtom, updatedNavItems);
    }
  );

  // atomInstances.set(bookingId, derivedAtom);
  return derivedAtom;
}

export const remainingPriceAtom = atom<string | null>(null);

export function atomWithReplaceVehicle() {
  const baseAtom = atomWithStorage<NavItem[]>(`replace-vehicle-selection`, {});

  const derivedAtom = atom(
    (get) => get(baseAtom),
    (get, set, updates: any) => {
      const currentRepalcedSelection = get(baseAtom);
      const updatedRepalcedSelection = updates;
      set(baseAtom, updatedRepalcedSelection);
    }
  );

  // atomInstances.set(bookingId, derivedAtom);
  return derivedAtom;
}
