import { TajeerAgreement } from "@/api/contracts/schema";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";

import Link from "next/link";
import { toSaudiZoned } from "@/lib/utils";
import { format } from "date-fns";
import { useTranslations } from "next-intl";

export default function AuthCard({ contract }: { contract: TajeerAgreement }) {
  const t = useTranslations("authorization");
  return (
    <>
      <section className="mb-2 rounded-lg bg-gray-50 p-4">
        <div className="items-top flex justify-between">
          <div>
            <label className="mb-1 text-sm leading-normal  text-gray-600">{t("contractNo")}</label>
            <p className="break-all text-sm font-medium leading-none  text-gray-900">
              {contract.externalAuthorizationNumber ?? "-"}
            </p>
          </div>
          <div className="">
            <label className="mb-1 text-sm leading-normal  text-gray-600">{t("authorizedOn")}</label>
            <p className="break-all text-sm font-medium leading-none ">
              {contract?.createdOn ? format(toSaudiZoned(contract?.createdOn * 1000), "dd/MM/yyyy") : "N/A"}
            </p>
            <small>{contract?.createdOn ? format(toSaudiZoned(contract?.createdOn * 1000), "HH:mm:ss") : "N/A"}</small>
          </div>
          <div className="">
            <label className="mb-1 text-sm leading-normal  text-gray-600">{t("vehicle")}</label>
            <p className="break-all text-sm font-medium leading-none ">-</p>
            <small>-</small>
          </div>
          {((contract.status === "SUCCESS" &&
            contract.type.toLowerCase() === "tamm" &&
            contract.metadata.confirmCancelDriverAuthStatus === "SUCCESS") ||
            (contract.status === "SUCCESS" &&
              contract.type.toLowerCase() === "tajeer" &&
              contract.metadata.closeStatus === "SUCCESS")) && (
            <>
              <div>
                <label className="mb-1 text-sm leading-normal  text-gray-600">{t("closedOn")}</label>
                <p className="break-all text-sm font-medium leading-none text-gray-900">
                  {contract.type.toLowerCase() === "tamm"
                    ? contract.metadata.confirmDriverAuthIssueDateTime
                      ? format(toSaudiZoned(contract.metadata.confirmDriverAuthIssueDateTime * 1000), "dd/MM/yyyy")
                      : "N/A"
                    : contract.metadata.saveDateTime
                      ? format(toSaudiZoned(contract.metadata.saveDateTime * 1000), "dd/MM/yyyy")
                      : "N/A"}
                </p>
                <small>
                  {contract.type.toLowerCase() === "tamm"
                    ? contract.metadata.confirmDriverAuthIssueDateTime
                      ? format(toSaudiZoned(contract.metadata.confirmDriverAuthIssueDateTime * 1000), "HH:mm:ss")
                      : "N/A"
                    : contract.metadata.saveDateTime
                      ? format(toSaudiZoned(contract.metadata.saveDateTime * 1000), "HH:mm:ss")
                      : "N/A"}
                </small>
              </div>
            </>
          )}

          {contract.status === "SUCCESS" && (
            <Link
              target="_blank"
              href={`/contract/pdf?referenceNumber=${contract.bookingNo}`}
              prefetch={false}
              className="gap-2"
            >
              <Button variant="outline" size={"sm"} className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                {t("cta.viewContract")}
              </Button>
            </Link>
          )}
        </div>
      </section>
      {contract.metadata.failureReason && (
        <section className="rounded-lg bg-gray-50 p-4">
          <header className="font-medium">{t("failureReason")}</header>
          <div className="flex items-center justify-between">
            <p className="break-all font-mono text-sm text-gray-900">
              {contract.metadata.failureReason?.desc || "No failure reason available"}
            </p>
            <p className="break-all text-sm font-medium">{contract.metadata.failureReason.timestamp}</p>
          </div>
        </section>
      )}
    </>
  );
}
