import { TajeerAgreement } from "@/api/contracts/schema";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

export default function AuthAction({
  contract,
  loading,
  onCancel,
  onRevalidate,
}: {
  contract: TajeerAgreement;
  loading: boolean;
  onCancel: () => void;
  onRevalidate: () => void;
}) {
  return (
    <>
      {contract?.status === "IN_PROGRESS" ? (
        <Button variant="outline" size="sm" onClick={onCancel} disabled={loading}>
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Cancel contract
        </Button>
      ) : (
        <Button variant="outline" size="sm" onClick={onRevalidate} disabled={loading}>
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Re-validate contract
        </Button>
      )}
    </>
  );
}
