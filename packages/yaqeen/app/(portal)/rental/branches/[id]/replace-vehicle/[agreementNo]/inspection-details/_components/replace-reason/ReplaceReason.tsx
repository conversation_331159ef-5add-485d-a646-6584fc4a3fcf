"use client";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useTranslations } from "next-intl";

interface ReplaceReasonProps {
  value: string;
  onValueChange: (value: string) => void;
}

export function ReplaceReason({ value, onValueChange }: ReplaceReasonProps) {
  const t = useTranslations("replace-reason");

  const reasons = [
    {
      id: "BREAKDOWN",
      label: t("BREAKDOWN.reason"),
      description: t("BREAKDOWN.description"),
    },
    {
      id: "MAJOR_DAMAGE",
      label: t("MAJOR_DAMAGE.reason"),
      description: t("MAJOR_DAMAGE.description"),
    },
    {
      id: "MAINTENANCE",
      label: t("MAINTENANCE.reason"),
      description: t("MAINTENANCE.description"),
    },
    {
      id: "CUSTOMER_UNSATISFIED",
      label: t("CUSTOMER_UNSATISFIED.reason"),
      description: t("CUSTOMER_UNSATISFIED.description"),
    },
  ];

  return (
    <Card className="flex flex-col shadow">
      <CardHeader>
        <CardTitle className="text-lg">{t("title")}</CardTitle>
        <p className="text-sm text-gray-500">{t("description")}</p>
      </CardHeader>
      <CardContent className="p-0">
        <RadioGroup value={value} onValueChange={onValueChange} className="gap-0">
          {reasons.map((reason) => (
            <div key={reason.id} className="mt-0 flex items-start border-t px-6 py-4">
              <RadioGroupItem
                value={reason.id}
                id={reason.id}
                className="mt-1 data-[state=checked]:border-blue-600 data-[state=checked]:bg-white"
              />
              <div className="ml-2 flex-1">
                <Label htmlFor={reason.id} className="cursor-pointer text-sm font-medium">
                  {reason.label}
                </Label>
                <p className="mt-1 text-xs text-gray-500">{reason.description}</p>
              </div>
            </div>
          ))}
        </RadioGroup>
      </CardContent>
    </Card>
  );
}
