"use client";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useTranslations } from "next-intl";

interface ReplaceReasonProps {
  value: string;
  onValueChange: (value: string) => void;
}

export function ReplaceReason({ value, onValueChange }: ReplaceReasonProps) {
  const t = useTranslations("replace-reason");

  const reasons = [
    {
      id: "BREAKDOWN",
      label: t("vehicle-issue"),
      description: t("vehicle-issue-description"),
    },
    {
      id: "MAJOR_DAMAGE",
      label: t("accident-damage"),
      description: t("accident-damage-description"),
    },
    {
      id: "MAINTENANCE",
      label: t("maintenance"),
      description: t("maintenance-description"),
    },
    {
      id: "CUSTOMER_UNSATISFIED",
      label: t("customer-unsatisfied"),
      description: t("customer-unsatisfied-description"),
    },
  ];

  return (
    <Card className="flex flex-col shadow">
      <CardHeader>
        <CardTitle className="text-lg">{t("title")}</CardTitle>
        <p className="text-sm text-gray-500">{t("description")}</p>
      </CardHeader>
      <CardContent>
        <RadioGroup value={value} onValueChange={onValueChange} className="space-y-4">
          {reasons.map((reason) => (
            <div key={reason.id} className="flex items-start space-x-3">
              <RadioGroupItem
                value={reason.id}
                id={reason.id}
                className="mt-1 data-[state=checked]:border-blue-600 data-[state=checked]:bg-white"
              />
              <div className="flex-1">
                <Label htmlFor={reason.id} className="cursor-pointer text-sm font-medium">
                  {reason.label}
                </Label>
                <p className="mt-1 text-xs text-gray-500">{reason.description}</p>
              </div>
            </div>
          ))}
        </RadioGroup>
      </CardContent>
    </Card>
  );
}
