"use client";

import React from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useTranslations } from "next-intl";

interface VehicleStatusCardProps {
  value: string;
  onValueChange: (value: string) => void;
  replaceReason: string;
}

export function VehicleStatusCard({ value, onValueChange, replaceReason }: VehicleStatusCardProps) {
  const t = useTranslations("vehicle-status");

  const statuses = [
    {
      id: "FUELING_CLEANING",
      label: t("needs-service"),
    },
    {
      id: "WORKSHOP_TRANSFER",
      label: t("workshop"),
    },
    {
      id: "READY",
      label: t("ready"),
    },
  ];

  // Auto-select and disable logic based on replace reason
  const getStatusAvailability = (statusId: string) => {
    switch (replaceReason) {
      case "MAJOR_DAMAGE":
      case "MAINTENANCE":
        // Auto-select workshop and disable others
        if (statusId === "WORKSHOP_TRANSFER") {
          return { disabled: false, autoSelected: true };
        }
        return { disabled: true, autoSelected: false };
      case "CUSTOMER_UNSATISFIED":
        // All options available
        return { disabled: false, autoSelected: false };
      default:
        return { disabled: false, autoSelected: false };
    }
  };

  // Auto-select workshop for accident/maintenance if no value is set
  const shouldAutoSelect = (statusId: string) => {
    const availability = getStatusAvailability(statusId);
    return availability.autoSelected && (!value || value === "");
  };

  // Handle auto-selection
  React.useEffect(() => {
    if ((replaceReason === "MAJOR_DAMAGE" || replaceReason === "MAINTENANCE") && (!value || value === "")) {
      onValueChange("WORKSHOP_TRANSFER");
    }
  }, [replaceReason, value, onValueChange]);

  return (
    <Card className="flex flex-col shadow">
      <CardHeader>
        <CardTitle className="text-lg">{t("title")}</CardTitle>
        <p className="text-sm text-gray-500">{t("description")}</p>
      </CardHeader>
      <CardContent className="p-0">
        <RadioGroup value={value} onValueChange={onValueChange} className="gap-0">
          {statuses.map((status) => {
            const availability = getStatusAvailability(status.id);
            return (
              <div key={status.id} className="flex items-center border-t px-6 py-4">
                <RadioGroupItem
                  value={status.id}
                  id={status.id}
                  disabled={availability.disabled}
                  className="disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:border-blue-600 data-[state=checked]:bg-white"
                />
                <Label
                  htmlFor={status.id}
                  className={`ml-2 cursor-pointer text-sm font-normal ${
                    availability.disabled ? "cursor-not-allowed text-gray-400" : ""
                  }`}
                >
                  {status.label}
                </Label>
              </div>
            );
          })}
        </RadioGroup>
      </CardContent>
    </Card>
  );
}
