import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";
import { useRef, useState } from "react";
import { TajeerState } from "./authorization/tajeer";
import { resendOTP, verifyOTP } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";

export default function ValidateCode({ contract, onConfirm }) {
  const codeInputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const resednOTP = async () => {
    debugger;
    try {
      setLoading(true);
      const otp = codeInputRef.current?.value ?? "";
      const response = await resendOTP(contract.agreementNo, contract.type?.toLowerCase(), otp);
      console.log(`\n HERE IS OTP VERIFY:: ${JSON.stringify(response)} \n`);

      if (response.status !== 200) {
        const description = "The OTP couldn’t be sent. Please try again.";
        toast({
          title: "Error",
          description,
          variant: "destructive",
        });
      }
    } catch (error) {
      const description = "The OTP couldn’t be sent. Please try again.";
      toast({
        title: "Error",
        description,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mb-4 mt-4 rounded-lg bg-blue-50 p-6">
      <h4 className="mb-4 text-base font-medium text-gray-700">Enter code</h4>
      <p className="mb-4 text-sm text-gray-600">We've sent a code to customer's mobile number</p>

      <div className="flex gap-3">
        <Input ref={codeInputRef} type="text" placeholder="Enter code" className="flex-1" disabled={loading} />
        <Button onClick={onConfirm} disabled={loading} className="bg-[#BED754] text-black hover:bg-[#BED754]/90">
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Validate code
        </Button>
      </div>

      <div className="mt-3 flex items-center justify-between text-sm">
        {/* <span className="text-gray-600">Resend code in 43s</span> */}
        <button className="text-blue-600 hover:underline" onClick={resednOTP} disabled={loading}>
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}resend code
        </button>
      </div>
    </div>
  );
}
