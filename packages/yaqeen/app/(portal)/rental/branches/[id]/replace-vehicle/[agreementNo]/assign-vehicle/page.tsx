import { Suspense } from "react";
import PricingBreakdown from "../pricing-breakdown";
import { api } from "@/api";
import { notFound } from "next/navigation";
import { ActionsBar } from "../actions-bar";
// import SelectedVehicle from "./_components/selected-vehicle";
// import SuggestedVehicles from "./_components/suggested-vehicles";
import type { BookingAddOns, BookingDiscount } from "@/api/contracts/booking/schema";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";
import { PricingBreakdownSkeleton } from "../../../bookings/[bookingId]/pricing-breakdown-skeleton";
import { AgreementInvoice } from "@/api/contracts/schema";
import SelectedVehicle from "../../../bookings/[bookingId]/assign-a-vehicle/_components/selected-vehicle";
import SuggestedVehicles from "../../../bookings/[bookingId]/assign-a-vehicle/_components/suggested-vehicles";
import { type Vehicle } from "../../../bookings/[bookingId]/assign-a-vehicle/_components/vehicle-card";

export default async function VehicleSelection({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string; agreementNo: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const _params = await params;
  const _searchParams = await searchParams;

  const suspenseKey = Object.entries(_params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;
  const agreementNo = (_params?.agreementNo as string) ?? "";
  const plateNo = (_searchParams?.plateNo as string) ?? "";

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (!agreementResponse?.body) {
    throw new Error(`Error: ${agreementResponse.status}`);
  }

  const agreement: AgreementInvoice = agreementResponse.body as AgreementInvoice;

  const [bookingResponse, suggestedVehiclesResponse] = await Promise.all([
    api.bookingDetails.getBookingById({
      params: {
        id: Number(agreement.bookingId),
      },
    }),
    api.suggestedVehicles.getSuggestedVehiclesV2({
      params: {
        quoteId: "",
        bookingId: Number(agreement.bookingId),
      },
    }),
  ]);

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  // Get driver UID for the DriverProfileSheet component
  const driverUId = agreement?.driver?.driverUId ?? "";
  const bookingAddons: BookingAddOns[] = bookingResponse.body.priceDetail?.addOns ?? [];
  const discountDetail: BookingDiscount | null = bookingResponse.body.priceDetail?.discountDetail ?? null;

  let vehicles: Vehicle[] = [];
  switch (suggestedVehiclesResponse.status) {
    case 200:
      vehicles = suggestedVehiclesResponse.body;
      break;
    case 404:
    case 400:
      vehicles = [];
      break;
    default:
      throw new Error("Failed to fetch suggested vehicles");
  }

  const isEmpty = vehicles.length === 0;
  const isOnlyDowngrade = vehicles.every((v) => v.preferenceType === "DOWNGRADE");

  // Filter out downgrade vehicles from the list unless downgrades are the only option available.
  if (!isOnlyDowngrade) {
    vehicles = vehicles.filter((v) => v.preferenceType !== "DOWNGRADE");
  }

  if (!isEmpty) {
    vehicles = vehicles.map((vehicle) => ({
      ...vehicle,
      ...(vehicle.unavailableAddOnIds?.length
        ? {
            unAvailableAddons: vehicle.unavailableAddOnIds
              .map((addonid) => bookingAddons?.find((adOn) => adOn.id === addonid))
              .filter((addon): addon is NonNullable<typeof addon> => addon !== undefined),
          }
        : {}),
      ...(vehicle.isPromoApplied ? { discountDetail } : {}),
    }));
  }

  const successCtaDisabled = (vehicles.length === 0 && !plateNo) || !plateNo;

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <SelectedVehicle
          className="max-w-3xl overflow-hidden"
          preSelectedVehicle={null}
          customerPreference={""}
          groupCode={""}
          isEmpty={!!isEmpty}
          isOnlyDowngrade={isOnlyDowngrade}
        />
        <SuggestedVehicles isReplaceFlow={true} vehicles={vehicles} assignedVehicle={null} show={false} />
        <ActionsBar
          successCtaDisabled={successCtaDisabled}
          bookingNo={agreement.bookingNo}
          agreementNo={agreement.agreementNo}
          className="w-full"
        />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown agreement={agreement} _searchParams={searchParams}>
            {driverUId && <SidesheetWrapper driverUId={driverUId} />}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
