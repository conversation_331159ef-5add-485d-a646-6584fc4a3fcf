// Use react query to fetch agreement details
import { Agreement } from "@/api/contracts/booking/schema";
import { useCustomQuery } from "@/lib/hooks/use-query";

export function useAgreement(agreementNo: string) {
  const {
    data: agreement,
    isLoading,
    isError,
    error,
  } = useCustomQuery<Agreement>(["agreement", agreementNo], `/next-api/agreement/${agreementNo}`, {
    enabled: !!agreementNo,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    agreement,
    isLoading,
    isError,
    error,
  };
}
