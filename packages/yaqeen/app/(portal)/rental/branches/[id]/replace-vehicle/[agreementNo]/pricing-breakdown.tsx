import { api } from "@/api";
import { type CalculatePrice } from "@/api/contracts/booking/schema";
import type { AgreementInvoice } from "@/api/contracts/schema";
import PricingBreakDownClient from "./pricing-breakdown-client";

export default async function PricingBreakdown({
  agreement,
  children,
}: {
  agreement: AgreementInvoice;
  children?: React.ReactNode;
}) {
  const { agreementNo } = agreement;
  const insuranceIds = [];
  const isInsuranceExist = agreement.priceDetail?.insuranceIds?.length;
  if (isInsuranceExist && agreement.priceDetail?.insuranceIds?.length) {
    insuranceIds.push(Number(agreement.priceDetail?.insuranceIds));
  }

  const priceCalculatorResponse = await api.bookingDetails.calculateAgreementPrice({
    params: {
      agreementNo,
    },
    query: {
      replacement: true,
    },
  });

  console.log(`\n HER IS PRICE:: ${JSON.stringify(priceCalculatorResponse)} \n`);
  const priceResponse: CalculatePrice = priceCalculatorResponse.body as CalculatePrice;
  if (priceCalculatorResponse.status !== 200) {
    return <div>There is a problem calculating the price for this booking</div>;
  }

  const branchesResponse = await api.branch.getDetailedBranchList({
    query: { page: 0, size: 1000 },
  });

  if (branchesResponse.status !== 200) {
    throw new Error(`Error: ${branchesResponse.status}`);
  }

  return (
    <PricingBreakDownClient
      priceResponse={priceResponse}
      agreement={agreement}
      branchesResponse={branchesResponse.body.data}
    >
      {children}
    </PricingBreakDownClient>
  );
}
