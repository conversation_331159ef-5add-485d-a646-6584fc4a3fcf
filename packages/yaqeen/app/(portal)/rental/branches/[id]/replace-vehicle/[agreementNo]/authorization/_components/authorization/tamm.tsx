"use client";

import { useState, useRef } from "react";
import { useParams } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/hooks/use-toast";
import { initiateTajeer, validateTajeer } from "@/lib/actions";
import { CheckCircle2, XCircle, Hourglass, Loader2 } from "lucide-react";
import Link from "next/link";
import type { TajeerAgreement } from "@/api/contracts/schema";
import AuthCard from "../auth-card";
import ValidateCode from "../validate-code";

export interface TajeerState {
  loading: boolean;
  showCodePanel: boolean;
  contract: TajeerAgreement;
  error: string | null;
  closureAttempted: boolean;
}

const Isnew = false;

export default function Tamm({
  contract,
  renderAction,
  loading,
  children,
}: {
  contract: TajeerAgreement;
  renderAction: any;
  loading: boolean;
  children: React.ReactNode;
}) {
  const params = useParams();
  const agreementNo = params.agreementNo as string;
  const { toast } = useToast();

  const [state, setState] = useState<TajeerState>({
    loading: false,
    showCodePanel: false,
    contract,
    error: null,
    closureAttempted: false,
  });

  // Mock data for demonstration - in real implementation, this would come from props or API
  const mockExistingContract = {
    contractNo: "2480699285910001",
    authorizedOn: "25/10/2024 - 08:25:34",
    vehicle: "Toyota Camry - BJZ 6352",
    status: "SUCCESS" as const,
  };

  const handleUpdateContract = async () => {
    debugger;
    //setState((prev) => ({ ...prev, loading: true, error: null }));
    // try {
    //   // Close the previous contract by initiating Tajeer with existing contract number
    //   const response = await initiateTajeer(agreementNo, mockExistingContract.contractNo);

    //   if (response.status === 200) {
    //     setState((prev) => ({
    //       ...prev,
    //       loading: false,
    //       showCodePanel: true,
    //       closureAttempted: true,
    //     }));
    //     toast({
    //       title: "Contract closure initiated",
    //       description: "Please enter the validation code",
    //     });
    //   } else {
    //     throw new Error("Failed to initiate contract closure");
    //   }
    // } catch (error) {
    //   setState((prev) => ({
    //     ...prev,
    //     loading: false,
    //     error: "Failed to close previous contract",
    //   }));
    //   toast({
    //     title: "Failed to close previous contract",
    //     description: "Please try again",
    //     variant: "destructive",
    //   });
    // }
  };

  const handleValidateCode = async () => {
    const code = codeInputRef.current?.value;
    if (!code) {
      toast({
        title: "Code required",
        description: "Please enter the validation code",
        variant: "destructive",
      });
      return;
    }

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const response = await validateTajeer(agreementNo, code);

      if (response.status === 200) {
        const contract = response.body as TajeerAgreement;
        setState((prev) => ({
          ...prev,
          loading: false,
          contracts: [contract],
          showCodePanel: false,
        }));
        toast({
          title: "Validation successful",
          description: "Contract has been validated",
        });
      } else {
        throw new Error("Validation failed");
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: "Validation failed",
      }));
      toast({
        title: "Validation failed",
        description: "Please check the code and try again",
        variant: "destructive",
      });
    }
  };

  const handleTryClosureAgain = () => {
    setState((prev) => ({
      ...prev,
      showCodePanel: false,
      error: null,
      closureAttempted: false,
    }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return (
          <Badge variant="secondary" className="h-6 bg-green-100 text-green-800">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            Authorized
          </Badge>
        );
      case "FAILED":
        return (
          <Badge variant="destructive" className="h-6">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      case "IN_PROGRESS":
        return (
          <Badge variant="outline" className="h-6">
            <Hourglass className="mr-1 h-3 w-3" />
            In progress
          </Badge>
        );
      default:
        return null;
    }
  };

  const authStatus = state.contract?.status;

  return (
    <div className="px-4">
      <section className="mb-4 flex flex-row items-center justify-between">
        <h3 className="flex items-center gap-2 text-lg font-semibold">
          <span>Tamm</span>
          {state.contract && getStatusBadge(authStatus || "UNKNOWN")}
        </h3>

        {renderAction}
      </section>
      {/* Show existing contract when no new contracts are available */}
      {state.contract?.status !== "IN_PROGRESS" &&
        state.contract?.metadata?.verifyDriverAuthIssueStatus === "SUCCESS" && (
          <div className="space-y-2">
            <AuthCard contract={state.contract} />
          </div>
        )}

      {children}
    </div>
  );
}
