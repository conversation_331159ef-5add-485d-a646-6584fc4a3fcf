import { <PERSON><PERSON><PERSON>Agreement } from "@/api/contracts/schema";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye } from "lucide-react";

export default function AuthCard({ contract }: { contract: <PERSON><PERSON>rAgreement }) {
  return (
    <>
      <section className="mb-2 rounded-lg bg-gray-50 p-4">
        <div className="items-top flex justify-between">
          <div>
            <label className="mb-1 text-sm leading-normal  text-gray-600">Contract no.</label>
            <p className="break-all text-sm font-medium leading-none  text-gray-900">
              {contract.externalAuthorizationNumber ?? "-"}
            </p>
          </div>
          <div className="">
            <label className="mb-1 text-sm leading-normal  text-gray-600">Authorized on</label>
            <p className="break-all text-sm font-medium leading-none ">{contract.createdOn}</p>
            <small>{contract.createdOn}</small>
          </div>
          <div className="">
            <label className="mb-1 text-sm leading-normal  text-gray-600">Vehicle</label>
            <p className="break-all text-sm font-medium leading-none ">Nissan Altima</p>
            <small>ABJ 5342</small>
          </div>
          {!contract.metadata.failureReason && contract.status === "SUCCESS" && (
            <>
              <div>
                <label className="mb-1 text-sm leading-normal  text-gray-600">Closed on</label>
                <p className="break-all text-sm font-medium leading-none text-gray-900">6/29/2025</p>
                <small>10:54:01 AM</small>
              </div>

              <Button variant="outline" size={"sm"} className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                View contract
              </Button>
            </>
          )}
        </div>
      </section>
      {contract.metadata.failureReason && (
        <section className="rounded-lg bg-gray-50 p-4">
          <header className="font-medium">Failure history</header>
          <div className="flex items-center justify-between">
            <p className="break-all font-mono text-sm text-gray-900">
              {contract.metadata.failureReason?.desc || "No failure reason available"}
            </p>
            <p className="break-all text-sm font-medium">{contract.metadata.failureReason.timestamp}</p>
          </div>
        </section>
      )}
    </>
  );
}
