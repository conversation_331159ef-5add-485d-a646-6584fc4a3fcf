"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Tajeer from "./tajeer";
import Tamm from "./tamm";
import { Separator } from "@/components/ui/separator";
import { TajeerAgreement } from "@/api/contracts/schema";
import { Button } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { useState } from "react";
import { cancelAuthContract, closeExistingContract, initiateAuthContract, verifyOTP } from "@/lib/actions";
import { Loader2 } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useRouter } from "next/navigation";
import ValidateCode from "../validate-code";

export default function Authorization({
  tajeerContracts,
  tammContracts,
}: {
  tajeerContracts: TajeerAgreement[];
  tammContracts: TajeerAgreement[];
}) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [contractToBeInitiated, setContractToBeInitiated] = useState<string | null>(null);
  const currentTajeer = tajeerContracts[tajeerContracts.length - 1] ?? null;
  const currentTamm = tammContracts[tammContracts.length - 1] ?? null;

  const isTammSuccess = currentTamm?.status === "SUCCESS";
  const isTajeerSuccess = currentTajeer?.status === "SUCCESS";

  const isTammInProgress =
    currentTamm?.metadata?.verifyDriverAuthIssueStatus === "SUCCESS" && !currentTamm?.metadata?.authorizationNumber;
  const isTajeerInProgress =
    currentTajeer?.status === "IN_PROGRESS" &&
    currentTajeer?.metadata?.otpStatus === "SUCCESS" &&
    (currentTajeer?.metadata?.createStatus === "PENDING" || currentTajeer?.metadata?.createStatus === "FAILED");

  const closeContract = async (agreement: TajeerAgreement) => {
    try {
      const response = await closeExistingContract(
        agreement?.agreementNo ?? "",
        agreement?.agreementVehicleId ?? "",
        agreement?.type?.toLocaleLowerCase()
      );

      if (response.status !== 200) {
        throw new Error("Failed to close contract");
      }

      toast({
        variant: "success",
        title: "Success",
        description: "Contract closed successfully",
      });
    } catch (error) {
      const description = "The contract couldn’t be closed. Please try again.";
      toast({
        title: "Error",
        description,
        variant: "destructive",
      });
    }
  };

  const revalidateAuthContract = async (type: string) => {
    debugger;

    try {
      void setIsLoading(true);

      const shouldCloseTamm =
        currentTamm && currentTamm?.metadata?.verifyDriverAuthIssueStatus == "SUCCESS" && isTammSuccess;
      const shouldCloseTajeer = currentTajeer && currentTajeer?.metadata?.closeStatus == "SUCCESS" && isTajeerSuccess;

      if (shouldCloseTajeer && shouldCloseTamm) {
        await Promise.all([closeContract(currentTajeer), closeContract(currentTamm)]);
      } else if (shouldCloseTajeer) {
        await closeContract(currentTajeer);
      } else if (shouldCloseTamm) {
        await closeContract(currentTamm);
      }

      let contractTobeInitiated = type.toLowerCase() === "tajeer" ? currentTajeer : currentTamm;

      if (!contractTobeInitiated) {
        contractTobeInitiated = {
          agreementNo: currentTajeer?.agreementNo ?? null,
          agreementVehicleId: currentTajeer?.agreementVehicleId ?? null,
          type: type.toUpperCase() as any,
        };
      }

      const response = await initiateAuthContract(
        contractTobeInitiated?.agreementNo ?? "",
        contractTobeInitiated?.agreementVehicleId ?? "",
        contractTobeInitiated?.type?.toLocaleLowerCase()
      );
      console.log(`\n HERE IS RESP:: ${JSON.stringify(response)} \n`);
      if (response.status !== 200) {
        const description = "The contract couldn’t be initiated. Please try again.";
        toast({
          title: "Error",
          description,
          variant: "destructive",
        });
      }
      // router.refresh();
    } catch (error) {
      const description = "The contract couldn’t be initiated. Please try again.";
      toast({
        title: "Error",
        description,
        variant: "destructive",
      });
    } finally {
      void setIsLoading(false);
    }
  };

  const cancelContract = async (type: string) => {
    debugger;

    try {
      void setIsLoading(true);

      const contractTobeInitiated = type.toLowerCase() === "tajeer" ? currentTajeer : currentTamm;

      const response = await cancelAuthContract(
        contractTobeInitiated?.agreementNo ?? "",
        contractTobeInitiated?.type?.toLocaleLowerCase()
      );
      console.log(`\n HERE IS RESP:: ${JSON.stringify(response)} \n`);
      if (response.status !== 200) {
        const description = "The contract couldn’t be canceled. Please try again.";
        toast({
          title: "Error",
          description,
          variant: "destructive",
        });
      }
      // router.refresh();
    } catch (error) {
      const description = "The contract couldn’t be canceled. Please try again.";
      toast({
        title: "Error",
        description,
        variant: "destructive",
      });
    } finally {
      void setIsLoading(false);
    }
  };

  const revalidateContract = (type: string) => {
    debugger;
    const shouldCloseTamm =
      currentTamm && currentTamm?.metadata?.verifyDriverAuthIssueStatus == "SUCCESS" && isTammSuccess;
    const shouldCloseTajeer = currentTajeer && currentTajeer?.metadata?.closeStatus == "SUCCESS" && isTajeerSuccess;

    if ((shouldCloseTajeer && shouldCloseTamm) || shouldCloseTajeer || shouldCloseTamm) {
      setContractToBeInitiated(type);
    } else {
      setContractToBeInitiated(null);
      revalidateAuthContract(type);
    }
  };

  const confirmAuthorization = async (otp: string, type: string) => {
    debugger;
    try {
      let contractTobeInitiated = type.toLowerCase() === "tajeer" ? currentTajeer : currentTamm;
      const response = await verifyOTP(
        contractTobeInitiated.agreementNo,
        contractTobeInitiated.type?.toLowerCase(),
        otp
      );
      console.log(`\n HERE IS OTP VEIFY:: ${JSON.stringify(response)} \n`);

      if (response.status !== 200) {
        const description = "The OTP couldn’t be validated. Please try again.";
        toast({
          title: "Error",
          description,
          variant: "destructive",
        });
      }
    } catch (error) {
      const description = "The OTP couldn’t be validated. Please try again.";
      toast({
        title: "Error",
        description,
        variant: "destructive",
      });
    } finally {
      //setLoading(false);
    }
  };

  return (
    <>
      <Card className="col-span-8 h-fit w-full overflow-hidden !p-0">
        <CardHeader className="flex flex-row items-center justify-between border-b p-4">
          <CardTitle className="text-lg">Authorization</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 p-0">
          <Tajeer
            loading={isLoading}
            contract={currentTajeer ?? undefined}
            renderAction={
              <>
                {currentTajeer?.status === "IN_PROGRESS" ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => cancelContract(currentTajeer?.type ?? "tajeer")}
                    disabled={isLoading}
                  >
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Cancel contract
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => revalidateContract(currentTajeer?.type ?? "tajeer")}
                    disabled={isLoading}
                  >
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Re-validate contract
                  </Button>
                )}
              </>
            }
          >
            {/* Code validation panel */}
            {isTajeerInProgress && (
              <ValidateCode contract={currentTajeer} onConfirm={(otp: string) => confirmAuthorization(otp, "tajeer")} />
            )}
          </Tajeer>
          <Separator className="my-0" />
          <Tamm
            loading={isLoading}
            contract={currentTamm ?? undefined}
            renderAction={
              <>
                {currentTamm?.status === "IN_PROGRESS" ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => cancelContract(currentTamm?.type ?? "tamm")}
                    disabled={isLoading}
                  >
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Cancel contract
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => revalidateContract(currentTamm?.type ?? "tamm")}
                    disabled={isLoading}
                  >
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Re-validate contract
                  </Button>
                )}
              </>
            }
          >
            {/* Code validation panel */}
            {isTammInProgress && (
              <ValidateCode contract={currentTajeer} onConfirm={(otp: string) => confirmAuthorization(otp, "tamm")} />
            )}
          </Tamm>
        </CardContent>
      </Card>

      <Dialog open={!!contractToBeInitiated} onOpenChange={() => setContractToBeInitiated(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update the contract?</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <p>This will close the current contract and initiate a new one</p>
          </div>
          <section className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setContractToBeInitiated(null)}>
              Cancel
            </Button>
            <Button variant="default" onClick={() => revalidateAuthContract(contractToBeInitiated ?? "")}>
              Confirm
            </Button>
          </section>
        </DialogContent>
      </Dialog>
    </>
  );
}
