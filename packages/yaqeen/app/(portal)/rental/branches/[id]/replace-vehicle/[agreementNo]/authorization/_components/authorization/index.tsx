import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card";
import <PERSON><PERSON><PERSON> from "./tajeer";
import Tamm from "./tamm";

export default function Authorization() {
  return (
    <>
      <Card className="col-span-8 h-fit w-full overflow-hidden !p-0">
        <CardHeader className="flex flex-row items-center justify-between border-b px-4 py-6">
          <CardTitle>Authorization</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 p-0">
          <Tajeer />
          <Tamm />
        </CardContent>
      </Card>
    </>
  );
}
