import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRef, useState } from "react";

export default function TajeerManual({
  loading,
  tajeerLink,
  onConfirm,
}: {
  loading: boolean;
  tajeerLink: string;
  onConfirm: (contractNumber: string) => void;
}) {
  const t = useTranslations("authorization");
  const codeInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="mb-4 mt-4 rounded-lg bg-blue-50 p-6">
      <h4 className="mb-4 text-base font-medium text-gray-700">{t("tajeer.enterContractNumber")}</h4>
      <p className="mb-4 text-sm text-gray-600">{t("tajeer.enterContractNumberDescription")}</p>

      <div className="flex gap-3">
        <Input
          ref={codeInputRef}
          type="text"
          placeholder="Enter contract number"
          className="flex-1"
          disabled={loading}
        />
        <Button
          onClick={() => onConfirm(codeInputRef.current?.value ?? "")}
          disabled={loading}
          className="bg-[#BED754] text-black hover:bg-[#BED754]/90"
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t("cta.submit")}
        </Button>
      </div>

      <div className="mt-3 flex items-center justify-between text-sm">
        {/* <span className="text-gray-600">Resend code in 43s</span> */}
        <a href={tajeerLink} target="_blank" className="text-blue-600 hover:underline">
          {t("tajeer.openTajeerWebsite")}
        </a>
      </div>
    </div>
  );
}
