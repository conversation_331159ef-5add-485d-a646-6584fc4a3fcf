import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { ActionsBar } from "../actions-bar";
import { PricingBreakdownSkeleton } from "../pricing-breakdown-skeleton";
import PricingBreakdown from "../pricing-breakdown";
import type { AgreementInvoice, TajeerAgreement } from "@/api/contracts/schema";
import type { ClosingPrice } from "@/api/contracts/booking/schema";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";
import Authorization from "./_components/authorization";
import SuccessContracts from "./_components/authorization/success-contracts";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ agreementNo: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const { agreementNo } = await params;
  const _searchParams = await searchParams;
  const vehicleStatus = (_searchParams?.vehicleStatus as string) ?? "";

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const closingPriceResponse = await api.booking.closingPrice({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (!closingPriceResponse?.body) {
    throw new Error(`Error: ${agreementResponse.status}`);
  }

  const agreement: AgreementInvoice = agreementResponse.body;
  const driverUId = agreement?.driver?.driverUId ?? "";

  const suggestedAuth = await api.booking.getSuggestedAuthorization({
    params: {
      bookingNo: agreement.bookingNo!,
    },
  });

  if (suggestedAuth.status !== 200) {
    throw new Error(`Error: ${suggestedAuth.status}`);
  }
  const suggestAuth = suggestedAuth?.body?.suggestedAuths?.[0]?.name ?? "";

  const authorizedContractsResp = await api.tajeer.getAllTajeerAgreements({
    query: {
      referenceNumber: agreement?.bookingNo ?? "",
    },
  });

  let authorizedContracts: TajeerAgreement[] = [];
  let tammContracts: TajeerAgreement[] = [];
  let tajeerContracts: TajeerAgreement[] = [];

  if (authorizedContractsResp.status === 200) {
    authorizedContracts = authorizedContractsResp?.body?.data ?? [];
    tammContracts = authorizedContracts.filter((contract) => contract.type === "TAMM");
    tajeerContracts = authorizedContracts.filter((contract) => contract.type === "TAJEER");
  }

  const tajeerLink =
    process.env.NODE_ENV === "production" ? "https://tajeer.tga.gov.sa/" : "https://tajeerstg.tga.gov.sa/#/";

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Authorization
          tajeerContracts={tajeerContracts}
          tammContracts={tammContracts}
          suggestAuth={suggestAuth}
          tajeerLink={tajeerLink}
        />
        <ActionsBar
          agreementNo={agreement.agreementNo}
          successCtaDisabled={agreement.status === "COMPLETED"}
          className="w-full"
        />
      </div>
      <div className="col-span-4">
        <Suspense fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown agreement={agreement}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
