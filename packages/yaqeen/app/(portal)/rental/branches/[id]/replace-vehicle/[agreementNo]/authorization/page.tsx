import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { ActionsBar } from "../actions-bar";
import { PricingBreakdownSkeleton } from "../pricing-breakdown-skeleton";
import PricingBreakdown from "../pricing-breakdown";
import type { AgreementInvoice, TajeerAgreement } from "@/api/contracts/schema";
import ValidateCode from "../../../bookings/[bookingId]/authorization/_components/ValidateCode";
import type { ClosingPrice } from "@/api/contracts/booking/schema";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";
import Authorization from "./_components/authorization";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ agreementNo: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const { agreementNo } = await params;
  const _searchParams = await searchParams;
  const vehicleStatus = (_searchParams?.vehicleStatus as string) ?? "";

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const agreement: AgreementInvoice = agreementResponse.body;

  // Fetch booking details to get driverUId
  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(agreement.bookingId),
    },
  });

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const getTajeerContracts = await api.tajeer.getAllTajeerAgreements({
    query: {
      referenceNumber: agreement?.bookingNo ?? "",
    },
  });

  console.log(`\n HERE IS TAJEER:: ${JSON.stringify(getTajeerContracts)} \n`);
  let tajeerContracts: TajeerAgreement[] = [];
  if (getTajeerContracts.status === 200) {
    tajeerContracts = getTajeerContracts?.body?.data ?? [];
  }
  const closingPriceResponse = await api.booking.closingPrice({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (!closingPriceResponse?.body) {
    throw new Error(`Error: ${agreementResponse.status}`);
  }

  const tajeerLink =
    process.env.NODE_ENV === "production" ? "https://tajeer.tga.gov.sa/" : "https://tajeerstg.tga.gov.sa/#/";

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Authorization />
        <ValidateCode
          tajeerLink={tajeerLink}
          bookingNo={agreement.bookingNo}
          tajeerContracts={tajeerContracts}
          disableSecurityDeposit
          displayOnly
        />
        {/* <VehicleStatus /> */}
        <ActionsBar
          closingPriceResponse={closingPriceResponse?.body as ClosingPrice}
          vehicleStatus={vehicleStatus}
          agreementNo={agreement.agreementNo}
          successCtaDisabled={agreement.status === "COMPLETED"}
          className="w-full"
        />
      </div>
      <div className="col-span-4">
        <Suspense fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown agreement={agreement}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
