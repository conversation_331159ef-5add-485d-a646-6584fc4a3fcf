import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { ActionsBar } from "../actions-bar";
import { PricingBreakdownSkeleton } from "../pricing-breakdown-skeleton";
import type { CalculatePrice } from "@/api/contracts/booking/schema";
import PricingBreakdown from "../pricing-breakdown";
import type { AgreementInvoice } from "@/api/contracts/schema";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";
import { InspectionReport } from "../../../close-agreements/[agreementNo]/inspection-details/_components/InspectionReport";
import ReplaceReasonAndVehicleStatusWrapper from "./_components/replace-reason";
import { BookingDetailAndRentalRateSkeleton } from "../../../bookings/[bookingId]/booking-details/components/skeleton/booking-detail-rental-skeleton";

export default async function Page({ params }: { params: Promise<{ agreementNo: string }> }) {
  const { agreementNo } = await params;

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const agreement: AgreementInvoice = agreementResponse.body;

  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(agreement.bookingId),
    },
  });

  console.log(`\n HERE IS bookingResponse:: ${JSON.stringify(bookingResponse)} \n`);
  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const BOOKING_DETAIL_KEY = `${suspenseKey}_booking_detail`;

  const priceCalculatorResponse = await api.bookingDetails.calculateAgreementPrice({
    params: {
      agreementNo,
    },
  });

  const priceResponse: CalculatePrice = priceCalculatorResponse.body as CalculatePrice;

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Suspense key={BOOKING_DETAIL_KEY} fallback={<BookingDetailAndRentalRateSkeleton />}>
          <ReplaceReasonAndVehicleStatusWrapper />
          <InspectionReport priceResponse={priceResponse} agreement={agreement} isVehicleReplacement={true} />
        </Suspense>
        <ActionsBar agreementNo={agreement.agreementNo} className="w-full" />
      </div>
      <div className="col-span-4">
        <Suspense fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown agreement={agreement}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
