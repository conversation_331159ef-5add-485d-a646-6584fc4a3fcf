import { TajeerAgreement } from "@/api/contracts/schema";

type UseFilteredContractsParams = {
  tajeerContracts: TajeerAgreement[];
  tammContracts: TajeerAgreement[];
};

const getFilteredSuccessfulContracts = (
  contracts: TajeerAgreement[],
  isClosed: (contract: TajeerAgreement) => boolean
) => {
  const successful = contracts.filter((c) => c.status === "SUCCESS");
  const hasClosed = successful.some(isClosed);
  const hasUnclosed = successful.some((c) => !isClosed(c));
  return hasClosed && hasUnclosed ? successful : [];
};

export function useAuthorizedContracts({ tajeerContracts, tammContracts }: UseFilteredContractsParams) {
  const getLastContract = (contracts: TajeerAgreement[]) => contracts[contracts.length - 1] ?? null;

  const isContractSuccessful = (contract?: TajeerAgreement | null) => contract?.status === "SUCCESS";

  const currentTajeer = getLastContract(tajeerContracts);
  const currentTamm = getLastContract(tammContracts);

  const isTajeerSuccess = isContractSuccessful(currentTajeer);
  const isTammSuccess = isContractSuccessful(currentTamm);

  const isTammInProgress =
    currentTamm?.metadata?.verifyDriverAuthIssueStatus === "SUCCESS" && !currentTamm?.externalAuthorizationNumber;

  const isTajeerInProgress =
    currentTajeer?.status === "IN_PROGRESS" &&
    currentTajeer?.metadata?.otpStatus === "SUCCESS" &&
    ["PENDING", "FAILED"].includes(currentTajeer?.metadata?.createStatus ?? "");

  const filteredTajeerSuccessfulContracts = getFilteredSuccessfulContracts(
    tajeerContracts,
    (c) => c.metadata?.closeStatus === "SUCCESS"
  );

  const filteredTammSuccessfulContracts = getFilteredSuccessfulContracts(
    tammContracts,
    (c) => c.metadata?.confirmCancelDriverAuthStatus === "SUCCESS"
  );

  return {
    isTammSuccess,
    isTajeerSuccess,
    isTammInProgress,
    isTajeerInProgress,
    currentTajeer,
    currentTamm,
    filteredTajeerSuccessfulContracts,
    filteredTammSuccessfulContracts,
  };
}
