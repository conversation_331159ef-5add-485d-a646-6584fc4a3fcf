import { MagnifyingGlass } from "@phosphor-icons/react";
import { ImageDialog } from "../../branches/[id]/close-agreements/[agreementNo]/inspection-details/_components/ImageDialog";
import { useTranslations } from "next-intl";

export function InspectionImages({ inspection, type }: { inspection: any; type: string }) {
  const t = useTranslations("replaceHistory");
  return (
    <>
      {!inspection ? (
        <div className="flex h-full w-full flex-col">
          <h4 className="mb-2">
            {type === "checkout" ? t("inspection_report.pickup_inspection") : t("inspection_report.dropoff_inspection")}
          </h4>
          <div className="flex h-full flex-col items-center justify-center gap-2 bg-slate-50 py-3">
            <MagnifyingGlass weight="regular" fontSize={24} className="text-slate-200" />
            <div className="text-sm text-slate-500">{t("inspection_report.no_inspection_details")}</div>
          </div>
        </div>
      ) : (
        <div className="flex w-full flex-col space-y-6">
          <h4 className="mb-2">
            {type === "checkout" ? t("inspection_report.pickup_inspection") : t("inspection_report.dropoff_inspection")}
          </h4>
          {inspection?.images?.length > 0 ? (
            <>
              {inspection?.images?.[0]?.imageUrl ? (
                <ImageDialog src={inspection.images[0].imageUrl} alt="Inspection image" width={342} height={450} />
              ) : null}
              <div className="flex gap-2">
                {inspection.images.slice(1).map((image, index) => (
                  <div key={`${image.typeId}-${index}`} className="relative aspect-square overflow-hidden rounded-lg">
                    <ImageDialog src={image.imageUrl} alt={`Inspection image ${image.typeId}`} width={64} height={64} />
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="flex w-full flex-col">
              <div className="flex h-full flex-col items-center justify-center gap-2 bg-slate-50 py-32">
                <MagnifyingGlass weight="regular" fontSize={64} className="text-slate-200" />
                <div className="text-slate-500">{t("inspection_report.no_inspection_details")}</div>
              </div>
            </div>
          )}
          {inspection?.remark && (
            <div>
              <div className="mb-1 flex">
                <div>{t("inspection_report.inspector_remarks")}</div>
                {inspection?.newDamage ? <div>{t("inspection_report.new_damage")}</div> : null}
              </div>
              <p className="rounded-lg bg-slate-100 p-2 text-slate-700">{inspection?.remark ?? "-"}</p>
            </div>
          )}
        </div>
      )}
    </>
  );
}
