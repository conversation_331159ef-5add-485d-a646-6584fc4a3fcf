import { VehiclePlate } from "../vehicle-plate";
import { getVehicleDetails } from "@/lib/actions";
import { useEffect, useState } from "react";

export function VehicleDetail({ plateNo, plateNoAr }: { plateNo: string; plateNoAr: string }) {
  const [vehicelDetail, setVehicleDetail] = useState<{
    make: string;
    model: string;
    year: string;
  } | null>(null);

  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchVehicleDetails = async () => {
      setLoading(true);
      try {
        const vehicleDetailsResponse = await getVehicleDetails(plateNo);

        if (vehicleDetailsResponse.status !== 200) {
          throw new Error("Failed to fetch vehicle details");
        }

        const locale = "en"; // TODO: Get locale from context
        const vehicleDetails = vehicleDetailsResponse.body;

        const make = vehicleDetails?.model?.make?.name?.[locale];
        const model = vehicleDetails?.model?.name?.[locale];
        const year = vehicleDetails?.modelYear;

        setVehicleDetail({
          make,
          model,
          year,
        });
      } catch (error) {
        console.error("Error fetching vehicle details:", error);
      } finally {
        setLoading(false);
      }
    };

    void fetchVehicleDetails();
  }, [plateNo]);

  return (
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-medium">
        {loading
          ? "Loading..."
          : `${vehicelDetail?.make ?? "-"} ${vehicelDetail?.model ?? "-"} ${vehicelDetail?.year ?? "-"}`}
      </h3>
      <div className="flex flex-col items-end"></div>
      <VehiclePlate
        className="w-[126px]"
        plateNumber={plateNo?.split(" ")[0]}
        plateLetters={plateNo?.split(" ")[1]}
        plateNoAr={plateNoAr}
      />
    </div>
  );
}
