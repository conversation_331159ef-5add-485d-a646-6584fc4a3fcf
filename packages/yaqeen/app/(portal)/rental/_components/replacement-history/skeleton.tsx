import { Skeleton } from "@/components/ui/skeleton";

export default function InspectionHistorySkeleton() {
  return (
    <>
      <section className="space-y-2 border-b p-4">
        <header className="flex flex-row justify-between gap-x-16">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </header>
        <section className="flex w-full flex-row justify-between gap-x-16">
          <section className="flex w-full flex-col space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </section>
          <section className="flex w-full flex-col space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </section>
        </section>
      </section>

      <section className="space-y-2 border-b p-4">
        <header className="flex flex-row justify-between gap-x-16">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </header>
        <section className="flex w-full flex-row justify-between gap-x-16">
          <section className="flex w-full flex-col space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </section>
          <section className="flex w-full flex-col space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </section>
        </section>
      </section>
    </>
  );
}
