import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const agreementNo = url.searchParams.get("agreementNo");

    if (!agreementNo) {
      return NextResponse.json({ error: "Agreement number is required" }, { status: 400 });
    }

    const response = await api.booking.getAgreement({
      params: {
        agreementNo: agreementNo,
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch agreement details" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching agreement details:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
